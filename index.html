<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>晶格信使 - The Lattice Messengers</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin: 0;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }
        
        .header p {
            font-size: 1.2em;
            margin: 10px 0;
            opacity: 0.9;
        }
        
        .main-content {
            display: grid;
            grid-template-columns: 1fr 400px;
            gap: 30px;
        }
        
        .game-area {
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
        }
        
        .control-panel {
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
        }
        
        .grid-container {
            display: flex;
            justify-content: center;
            margin-bottom: 20px;
        }
        
        .energy-grid {
            display: grid;
            grid-template-columns: repeat(20, 25px);
            grid-template-rows: repeat(20, 25px);
            gap: 1px;
            border: 2px solid rgba(255,255,255,0.3);
            border-radius: 8px;
            padding: 5px;
            background: rgba(0,0,0,0.2);
        }
        
        .grid-cell {
            width: 25px;
            height: 25px;
            border-radius: 2px;
            position: relative;
            transition: all 0.3s ease;
        }
        
        .messenger {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 20px;
            height: 20px;
            border-radius: 50%;
            border: 2px solid white;
            z-index: 10;
            animation: pulse 2s infinite;
        }
        
        .messenger.red {
            background: #ff4444;
            box-shadow: 0 0 10px rgba(255, 68, 68, 0.8);
        }
        
        .messenger.blue {
            background: #4444ff;
            box-shadow: 0 0 10px rgba(68, 68, 255, 0.8);
        }
        
        @keyframes pulse {
            0%, 100% { transform: translate(-50%, -50%) scale(1); }
            50% { transform: translate(-50%, -50%) scale(1.1); }
        }
        
        .game-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .info-card {
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
            padding: 15px;
            text-align: center;
        }
        
        .info-card h3 {
            margin: 0 0 10px 0;
            font-size: 1.1em;
        }
        
        .info-card .value {
            font-size: 2em;
            font-weight: bold;
            margin: 5px 0;
        }
        
        .red-team { color: #ff6b6b; }
        .blue-team { color: #4dabf7; }
        
        .controls {
            margin-bottom: 20px;
        }
        
        .btn {
            background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            margin: 5px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }
        
        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }
        
        .btn.danger {
            background: linear-gradient(45deg, #ff6b6b 0%, #ee5a24 100%);
        }
        
        .btn.success {
            background: linear-gradient(45deg, #51cf66 0%, #40c057 100%);
        }
        
        .status {
            margin: 15px 0;
            padding: 10px;
            border-radius: 8px;
            text-align: center;
        }
        
        .status.connected {
            background: rgba(81, 207, 102, 0.2);
            border: 1px solid #51cf66;
        }
        
        .status.disconnected {
            background: rgba(255, 107, 107, 0.2);
            border: 1px solid #ff6b6b;
        }
        
        .training-stats {
            margin-top: 20px;
        }
        
        .stat-row {
            display: flex;
            justify-content: space-between;
            margin: 8px 0;
            padding: 5px 0;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255,255,255,0.1);
            border-radius: 4px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #51cf66 0%, #40c057 100%);
            transition: width 0.3s ease;
        }
        
        .legend {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 15px;
            font-size: 0.9em;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 3px;
        }
        
        @media (max-width: 1200px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .energy-grid {
                grid-template-columns: repeat(20, 20px);
                grid-template-rows: repeat(20, 20px);
            }
            
            .grid-cell {
                width: 20px;
                height: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔮 晶格信使</h1>
            <p>The Lattice Messengers - AI进化模拟游戏</p>
        </div>
        
        <div class="main-content">
            <div class="game-area">
                <div class="game-info">
                    <div class="info-card">
                        <h3>回合进度</h3>
                        <div class="value" id="turn-info">0 / 100</div>
                        <div class="progress-bar">
                            <div class="progress-fill" id="turn-progress"></div>
                        </div>
                    </div>
                    <div class="info-card">
                        <h3>游戏状态</h3>
                        <div class="value" id="game-status">等待开始</div>
                    </div>
                </div>
                
                <div class="game-info">
                    <div class="info-card red-team">
                        <h3>🔴 红队得分</h3>
                        <div class="value" id="red-score">0</div>
                        <small>共鸣长链²</small>
                    </div>
                    <div class="info-card blue-team">
                        <h3>🔵 蓝队得分</h3>
                        <div class="value" id="blue-score">0</div>
                        <small>共鸣长链²</small>
                    </div>
                </div>
                
                <div class="grid-container">
                    <div class="energy-grid" id="energy-grid"></div>
                </div>
                
                <div class="legend">
                    <div class="legend-item">
                        <div class="legend-color" style="background: #000080;"></div>
                        <span>深蓝 (-10)</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color" style="background: #808080;"></div>
                        <span>中性 (0)</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color" style="background: #ff0000;"></div>
                        <span>亮红 (+10)</span>
                    </div>
                </div>
            </div>
            
            <div class="control-panel">
                <div class="status" id="connection-status">
                    <span id="status-text">连接中...</span>
                </div>
                
                <div class="controls">
                    <button class="btn success" id="start-training" onclick="startTraining()">
                        🚀 开始训练
                    </button>
                    <button class="btn danger" id="stop-training" onclick="stopTraining()" disabled>
                        ⏹️ 停止训练
                    </button>
                    <button class="btn" id="play-game" onclick="playGame()">
                        🎮 观看对战
                    </button>
                </div>
                
                <div class="training-stats" id="training-stats">
                    <h3>📊 训练统计</h3>
                    <div class="stat-row">
                        <span>训练回合:</span>
                        <span id="episodes">0</span>
                    </div>
                    <div class="stat-row">
                        <span>红队胜利:</span>
                        <span id="red-wins" class="red-team">0</span>
                    </div>
                    <div class="stat-row">
                        <span>蓝队胜利:</span>
                        <span id="blue-wins" class="blue-team">0</span>
                    </div>
                    <div class="stat-row">
                        <span>平局:</span>
                        <span id="draws">0</span>
                    </div>
                    <div class="stat-row">
                        <span>探索率:</span>
                        <span id="epsilon">100%</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="game.js"></script>
</body>
</html>
