#!/usr/bin/env python3
"""
晶格信使离线演示版本
不需要websockets依赖，可以直接运行核心功能
"""

import numpy as np
import time
import os
import sys

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from game_engine import LatticeMessengersGame, Action, Team
import config

def clear_screen():
    """清屏"""
    os.system('cls' if os.name == 'nt' else 'clear')

def print_colored_grid(energy_grid, red_messengers, blue_messengers):
    """打印彩色游戏网格"""
    print("\n" + "="*60)
    print("🔮 晶格信使 - 当前游戏状态")
    print("="*60)
    
    # 创建显示网格
    display_grid = np.full((config.GRID_SIZE, config.GRID_SIZE), "⚪", dtype=object)
    
    # 填充能量值
    for y in range(config.GRID_SIZE):
        for x in range(config.GRID_SIZE):
            energy = energy_grid[y, x]
            if energy > config.RED_RESONANCE_THRESHOLD:
                display_grid[y, x] = "🔴"  # 红色共鸣
            elif energy < config.BLUE_RESONANCE_THRESHOLD:
                display_grid[y, x] = "🔵"  # 蓝色共鸣
            elif energy > 2:
                display_grid[y, x] = "🟡"  # 正能量
            elif energy < -2:
                display_grid[y, x] = "🟦"  # 负能量
            elif energy > 0:
                display_grid[y, x] = "🟠"  # 弱正能量
            elif energy < 0:
                display_grid[y, x] = "🟣"  # 弱负能量
            else:
                display_grid[y, x] = "⚪"  # 中性
    
    # 添加信使
    for messenger in red_messengers:
        display_grid[messenger.y, messenger.x] = "🔺"  # 红队信使
    
    for messenger in blue_messengers:
        display_grid[messenger.y, messenger.x] = "🔻"  # 蓝队信使
    
    # 打印网格头部
    print("   ", end="")
    for x in range(0, config.GRID_SIZE, 2):
        print(f"{x:2}", end="")
    print()
    
    # 打印网格
    for y in range(config.GRID_SIZE):
        if y % 2 == 0:  # 只显示偶数行以节省空间
            print(f"{y:2} ", end="")
            for x in range(config.GRID_SIZE):
                print(display_grid[y, x], end="")
            print()

def simulate_random_game():
    """模拟随机游戏"""
    print("🎮 开始随机游戏模拟...")
    
    game = LatticeMessengersGame()
    state = game.reset()
    
    turn = 0
    while not state.is_game_over() and turn < 20:  # 限制演示回合数
        # 随机选择动作
        red_actions = [np.random.choice(list(Action)) for _ in range(3)]
        blue_actions = [np.random.choice(list(Action)) for _ in range(3)]
        
        # 执行动作
        state, rewards, done = game.step(red_actions, blue_actions)
        turn += 1
        
        # 显示状态
        clear_screen()
        print_colored_grid(state.energy_grid, state.red_messengers, state.blue_messengers)
        
        # 显示信息
        red_score, blue_score = state.calculate_resonance_chains()
        print(f"\n回合: {state.turn}/{state.max_turns}")
        print(f"红队得分: {red_score} | 蓝队得分: {blue_score}")
        print(f"红队动作: {[a.name for a in red_actions]}")
        print(f"蓝队动作: {[a.name for a in blue_actions]}")
        
        if red_score > blue_score:
            print("🔴 红队领先!")
        elif blue_score > red_score:
            print("🔵 蓝队领先!")
        else:
            print("⚪ 双方平局!")
        
        print("\n按 Ctrl+C 停止演示...")
        time.sleep(2)  # 暂停2秒
        
        if done:
            break
    
    # 最终结果
    print("\n" + "="*60)
    print("🏆 游戏结束!")
    red_score, blue_score = state.calculate_resonance_chains()
    print(f"最终得分: 红队 {red_score} vs 蓝队 {blue_score}")
    
    if red_score > blue_score:
        print("🎉 红队获胜!")
    elif blue_score > red_score:
        print("🎉 蓝队获胜!")
    else:
        print("🤝 平局!")

def simulate_ai_training():
    """模拟AI训练过程"""
    print("🤖 开始AI训练模拟...")
    
    try:
        from ai_model import AITrainer
        
        trainer = AITrainer()
        
        for episode in range(10):
            print(f"\n训练回合 {episode + 1}/10...")
            
            red_score, blue_score, turns = trainer.train_episode()
            stats = trainer.get_training_stats()
            
            print(f"结果: 红队{red_score} vs 蓝队{blue_score} (用时{turns}回合)")
            print(f"统计: 红胜{stats['red_wins']} 蓝胜{stats['blue_wins']} 平局{stats['draws']}")
            print(f"探索率: {stats['red_epsilon']:.3f}")
            
            time.sleep(1)
        
        print("\n🎮 进行AI对战演示...")
        final_state, history = trainer.play_game()
        
        # 显示部分历史
        for i, game_info in enumerate(history[::10]):  # 每10回合显示一次
            clear_screen()
            print(f"AI对战回合 {game_info['turn']}")
            
            # 重建网格状态用于显示
            energy_grid = np.array(game_info['energy_grid'])
            red_messengers = [type('Messenger', (), {'x': m['x'], 'y': m['y']}) for m in game_info['red_messengers']]
            blue_messengers = [type('Messenger', (), {'x': m['x'], 'y': m['y']}) for m in game_info['blue_messengers']]
            
            print_colored_grid(energy_grid, red_messengers, blue_messengers)
            print(f"得分: 红队{game_info['red_score']} vs 蓝队{game_info['blue_score']}")
            
            time.sleep(1)
        
        final_info = history[-1]
        print(f"\n🏆 AI对战结束!")
        print(f"最终得分: 红队{final_info['red_score']} vs 蓝队{final_info['blue_score']}")
        
    except ImportError as e:
        print(f"❌ AI模块导入失败: {e}")
        print("可能缺少PyTorch依赖，请运行: pip install torch")

def show_energy_patterns():
    """展示能量模式"""
    print("🌈 能量模式演示")
    
    # 创建测试模式
    patterns = [
        ("红色十字", lambda x, y: 8.0 if (x == 10 or y == 10) else 0.0),
        ("蓝色圆环", lambda x, y: -7.0 if 8 <= ((x-10)**2 + (y-10)**2)**0.5 <= 10 else 0.0),
        ("棋盘模式", lambda x, y: 5.0 if (x + y) % 2 == 0 else -5.0),
        ("渐变模式", lambda x, y: (x - 10) * 0.8)
    ]
    
    for name, pattern_func in patterns:
        clear_screen()
        print(f"🎨 {name}")
        
        # 创建模式网格
        energy_grid = np.zeros((config.GRID_SIZE, config.GRID_SIZE))
        for y in range(config.GRID_SIZE):
            for x in range(config.GRID_SIZE):
                energy_grid[y, x] = pattern_func(x, y)
        
        # 显示
        print_colored_grid(energy_grid, [], [])
        print("\n按回车继续...")
        input()

def main_menu():
    """主菜单"""
    while True:
        clear_screen()
        print("🔮 晶格信使 - 离线演示版")
        print("="*50)
        print("1. 随机游戏模拟")
        print("2. AI训练演示")
        print("3. 能量模式展示")
        print("4. 退出")
        print("="*50)
        
        choice = input("请选择 (1-4): ").strip()
        
        try:
            if choice == "1":
                simulate_random_game()
            elif choice == "2":
                simulate_ai_training()
            elif choice == "3":
                show_energy_patterns()
            elif choice == "4":
                print("👋 再见!")
                break
            else:
                print("❌ 无效选择，请重试")
                time.sleep(1)
                continue
                
            print("\n按回车返回主菜单...")
            input()
            
        except KeyboardInterrupt:
            print("\n\n⏹️ 演示被中断")
            print("按回车返回主菜单...")
            input()
        except Exception as e:
            print(f"\n❌ 发生错误: {e}")
            print("按回车返回主菜单...")
            input()

if __name__ == "__main__":
    try:
        main_menu()
    except KeyboardInterrupt:
        print("\n\n👋 程序退出")
    except Exception as e:
        print(f"\n❌ 程序错误: {e}")
        input("按回车退出...")
