# 🔮 晶格信使 (The Lattice Messengers)

一个基于AI神经网络的进化模拟游戏，红蓝两队信使在20x20能量网格中进行策略对战。

## 🎮 游戏规则

### 游戏世界
- **网格**: 20x20晶格，每个位置具有能量值(-10到+10)
- **初始状态**: 所有晶格能量值为0

### 游戏角色
- **红队**: 3个信使，目标创造正能量区域
- **蓝队**: 3个信使，目标创造负能量区域
- **初始位置**: 
  - 红队: (2,2), (2,17), (17,2)
  - 蓝队: (17,17), (17,2), (2,17)

### 行动机制
- **移动**: 上下左右移动 (消耗0.1能量)
- **咏唱**: 
  - 红队信使: 当前位置+1能量 (消耗0.2能量)
  - 蓝队信使: 当前位置-1能量 (消耗0.2能量)

### 胜利条件
- **游戏时长**: 100回合
- **计分方式**: 最长共鸣长链的平方
  - 红队: 寻找能量>+5的连通区域
  - 蓝队: 寻找能量<-5的连通区域

## 🚀 快速开始

### 安装依赖
```bash
# 标准安装
pip install -r requirements.txt

# 如果网络问题，尝试最小依赖
pip install -r requirements_minimal.txt

# 或手动安装
pip install torch numpy websockets
```

### 启动游戏
```bash
python main.py
```

### 访问界面
打开浏览器访问: http://localhost:8000

## 🎯 操作指南

### Web界面控制
- **开始训练**: 让AI开始学习对战策略
- **停止训练**: 暂停AI训练过程
- **观看对战**: 观看训练好的AI实时对战

### 键盘快捷键
- `S` - 开始训练
- `X` - 停止训练
- `P` - 观看对战

## 🧠 AI系统

### 神经网络架构
- **输入层**: 413维状态向量
  - 400维: 20x20能量网格
  - 12维: 6个信使位置坐标
  - 1维: 当前回合进度
- **隐藏层**: 256 → 256 → 128 全连接层
- **输出层**: 
  - 动作概率: 5个动作的softmax分布
  - 状态价值: 单一价值评估

### 训练算法
- **算法**: Actor-Critic强化学习
- **经验回放**: 10000步缓冲区
- **探索策略**: ε-贪婪 (初始100%，衰减至1%)
- **优化器**: Adam优化器

## 📊 可视化功能

### 实时显示
- **能量网格**: 颜色映射显示能量分布
  - 深蓝(-10) → 灰色(0) → 亮红(+10)
- **信使位置**: 红蓝圆点标记，带脉动动画
- **得分统计**: 实时显示双方共鸣长链得分
- **训练进度**: 胜负统计、探索率等

### 颜色编码
- 🔴 红色区域: 正能量(红队优势)
- 🔵 蓝色区域: 负能量(蓝队优势)
- ⚪ 灰色区域: 中性能量
- 🔴 红色信使: 红队单位
- 🔵 蓝色信使: 蓝队单位

## 🏗️ 项目结构

```
lattice-messengers/
├── main.py              # 主启动文件
├── game_engine.py       # 游戏核心逻辑
├── ai_model.py          # AI神经网络模型
├── web_server.py        # Web服务器和WebSocket
├── index.html           # 前端界面
├── game.js              # 前端JavaScript逻辑
├── requirements.txt     # Python依赖
└── README.md           # 项目说明
```

## 🔧 技术栈

### 后端
- **Python 3.8+**: 主要开发语言
- **PyTorch**: 深度学习框架
- **NumPy**: 数值计算
- **WebSockets**: 实时通信

### 前端
- **HTML5**: 页面结构
- **CSS3**: 样式和动画
- **JavaScript**: 交互逻辑
- **WebSocket API**: 实时数据传输

## 🎨 特色功能

### AI进化
- 自主学习最优策略
- 动态平衡探索与利用
- 多智能体协作学习

### 实时可视化
- 流畅的动画效果
- 直观的能量分布显示
- 实时的训练统计

### 用户体验
- 响应式设计
- 键盘快捷键支持
- 自动重连机制

## 📈 性能优化

- 批量神经网络训练
- 异步WebSocket通信
- 高效的连通性算法
- 内存优化的经验回放

## 🔧 故障排除

### 依赖安装问题
如果遇到网络问题或依赖安装失败：

```bash
# 方法1: 使用最小依赖
pip install -r requirements_minimal.txt

# 方法2: 手动安装核心依赖
pip install torch numpy

# 方法3: 运行离线演示版本
python offline_demo.py
```

### 常见问题

**Q: WebSocket连接失败**
A: 检查防火墙设置，确保8765端口未被占用

**Q: PyTorch安装失败**
A: 访问 https://pytorch.org 获取适合您系统的安装命令

**Q: 游戏界面无法访问**
A: 确认HTTP服务器已启动，检查8000端口是否被占用

**Q: AI训练很慢**
A: 这是正常现象，神经网络训练需要时间。可以观看实时统计了解进度

### 备用运行方式

如果完整版本无法运行，可以使用以下备用方案：

1. **离线演示版**: `python offline_demo.py`
   - 不需要websockets依赖
   - 包含游戏核心功能演示
   - 支持AI训练模拟

2. **基础测试**: `python test_basic.py`
   - 验证核心功能是否正常
   - 快速检测问题所在

3. **功能演示**: `python demo.py`
   - 展示游戏各个组件
   - 适合了解游戏机制

## 🔮 未来扩展

- 更复杂的地形和障碍
- 多种信使类型和能力
- 锦标赛模式
- AI策略分析工具
- 自定义游戏规则

## 📝 许可证

本项目仅供学习和研究使用。

---

**享受AI进化的奇妙旅程！** 🚀✨
