2025-06-21 14:32:42,221 - __main__ - INFO - 使用简化版WebSocket服务器
2025-06-21 14:32:42,224 - __main__ - INFO - 正在启动晶格信使游戏服务器...
2025-06-21 14:32:42,225 - __main__ - INFO - HTTP服务器将在端口 8000 启动
2025-06-21 14:32:42,226 - __main__ - INFO - WebSocket服务器将在端口 8765 启动
2025-06-21 14:32:42,226 - __main__ - INFO - 按 Ctrl+C 停止服务器
2025-06-21 14:32:42,233 - web_server_simple - INFO - WebSocket服务器启动在 ws://localhost:8765
2025-06-21 14:32:42,261 - websockets.server - INFO - server listening on 127.0.0.1:8765
2025-06-21 14:32:42,262 - websockets.server - INFO - server listening on [::1]:8765
2025-06-21 14:32:42,279 - web_server_simple - INFO - HTTP服务器启动在 http://localhost:8000
2025-06-21 14:32:44,553 - websockets.server - INFO - connection open
2025-06-21 14:32:44,555 - websockets.server - ERROR - connection handler failed
Traceback (most recent call last):
  File "E:\car\ai2\venv\lib\site-packages\websockets\asyncio\server.py", line 376, in conn_handler
    await self.handler(connection)
TypeError: websocket_handler() missing 1 required positional argument: 'path'
2025-06-21 14:32:48,553 - websockets.server - INFO - connection open
2025-06-21 14:32:48,554 - websockets.server - ERROR - connection handler failed
Traceback (most recent call last):
  File "E:\car\ai2\venv\lib\site-packages\websockets\asyncio\server.py", line 376, in conn_handler
    await self.handler(connection)
TypeError: websocket_handler() missing 1 required positional argument: 'path'
2025-06-21 14:32:49,952 - web_server_simple - INFO - 服务器被用户中断
2025-06-21 14:32:49,953 - __main__ - INFO - 服务器已关闭
