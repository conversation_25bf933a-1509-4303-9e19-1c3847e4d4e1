#!/usr/bin/env python3
"""
晶格信使 (The Lattice Messengers) - AI进化模拟游戏
主启动文件

作者: AI Assistant
版本: 1.0.0
"""

import sys
import os
import logging
import argparse
from web_server import main as start_server

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('lattice_messengers.log', encoding='utf-8')
    ]
)

logger = logging.getLogger(__name__)

def check_dependencies():
    """检查必要的依赖包"""
    required_packages = [
        'torch',
        'numpy',
        'websockets'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        logger.error(f"缺少必要的依赖包: {', '.join(missing_packages)}")
        logger.info("请运行以下命令安装依赖:")
        logger.info("pip install torch numpy websockets")
        return False
    
    return True

def print_banner():
    """打印游戏横幅"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║                    🔮 晶格信使 🔮                              ║
    ║                The Lattice Messengers                        ║
    ║                                                              ║
    ║                   AI进化模拟游戏                              ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    
    游戏说明:
    • 20x20 能量网格世界
    • 红蓝两队各3个信使
    • 通过咏唱改造世界能量
    • 目标: 创造最长的共鸣长链
    
    控制说明:
    • 访问 http://localhost:8000 查看游戏界面
    • 点击"开始训练"让AI学习对战
    • 点击"观看对战"查看AI实时对战
    
    快捷键:
    • S - 开始训练
    • X - 停止训练  
    • P - 观看对战
    
    """
    print(banner)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='晶格信使 AI进化模拟游戏')
    parser.add_argument('--debug', action='store_true', help='启用调试模式')
    parser.add_argument('--http-port', type=int, default=8000, help='HTTP服务器端口')
    parser.add_argument('--ws-port', type=int, default=8765, help='WebSocket服务器端口')
    
    args = parser.parse_args()
    
    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)
        logger.debug("调试模式已启用")
    
    # 打印横幅
    print_banner()
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    logger.info("正在启动晶格信使游戏服务器...")
    logger.info(f"HTTP服务器将在端口 {args.http_port} 启动")
    logger.info(f"WebSocket服务器将在端口 {args.ws_port} 启动")
    logger.info("按 Ctrl+C 停止服务器")
    
    try:
        # 启动服务器
        start_server()
    except KeyboardInterrupt:
        logger.info("收到停止信号，正在关闭服务器...")
    except Exception as e:
        logger.error(f"服务器启动失败: {e}")
        sys.exit(1)
    finally:
        logger.info("服务器已关闭")

if __name__ == "__main__":
    main()
