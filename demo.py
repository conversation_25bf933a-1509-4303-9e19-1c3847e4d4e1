#!/usr/bin/env python3
"""
晶格信使演示脚本
用于快速测试游戏核心功能
"""

import numpy as np
from game_engine import LatticeMessengersGame, Action, Team
from ai_model import AITrainer
import config

def print_grid(energy_grid, red_messengers, blue_messengers):
    """打印游戏网格状态"""
    print("\n" + "="*50)
    print("当前游戏状态:")
    print("="*50)
    
    # 创建显示网格
    display_grid = np.full((config.GRID_SIZE, config.GRID_SIZE), "  ", dtype=object)
    
    # 填充能量值
    for y in range(config.GRID_SIZE):
        for x in range(config.GRID_SIZE):
            energy = energy_grid[y, x]
            if energy > 5:
                display_grid[y, x] = "🔴"  # 红色共鸣
            elif energy < -5:
                display_grid[y, x] = "🔵"  # 蓝色共鸣
            elif energy > 0:
                display_grid[y, x] = "🟡"  # 正能量
            elif energy < 0:
                display_grid[y, x] = "🟦"  # 负能量
            else:
                display_grid[y, x] = "⚪"  # 中性
    
    # 添加信使
    for messenger in red_messengers:
        display_grid[messenger.y, messenger.x] = "🔺"  # 红队信使
    
    for messenger in blue_messengers:
        display_grid[messenger.y, messenger.x] = "🔻"  # 蓝队信使
    
    # 打印网格
    print("   ", end="")
    for x in range(config.GRID_SIZE):
        print(f"{x:2}", end="")
    print()
    
    for y in range(config.GRID_SIZE):
        print(f"{y:2} ", end="")
        for x in range(config.GRID_SIZE):
            print(display_grid[y, x], end="")
        print()

def demo_basic_game():
    """演示基本游戏功能"""
    print("🔮 晶格信使 - 基本游戏演示")
    print("="*50)
    
    game = LatticeMessengersGame()
    state = game.reset()
    
    print(f"初始状态:")
    print(f"网格大小: {state.grid_size}x{state.grid_size}")
    print(f"最大回合数: {state.max_turns}")
    print(f"红队信使数量: {len(state.red_messengers)}")
    print(f"蓝队信使数量: {len(state.blue_messengers)}")
    
    # 显示初始网格
    print_grid(state.energy_grid, state.red_messengers, state.blue_messengers)
    
    # 模拟几个回合
    for turn in range(5):
        print(f"\n回合 {turn + 1}:")
        
        # 随机动作
        red_actions = [Action.CHANT, Action.MOVE_RIGHT, Action.MOVE_DOWN]
        blue_actions = [Action.CHANT, Action.MOVE_LEFT, Action.MOVE_UP]
        
        state, rewards, done = game.step(red_actions, blue_actions)
        
        print(f"红队动作: {[action.name for action in red_actions]}")
        print(f"蓝队动作: {[action.name for action in blue_actions]}")
        print(f"奖励: 红队{rewards[:3]}, 蓝队{rewards[3:]}")
        
        red_score, blue_score = state.calculate_resonance_chains()
        print(f"当前得分: 红队{red_score}, 蓝队{blue_score}")
        
        if turn % 2 == 0:  # 每两回合显示一次网格
            print_grid(state.energy_grid, state.red_messengers, state.blue_messengers)
        
        if done:
            break
    
    print(f"\n演示结束！")

def demo_ai_training():
    """演示AI训练功能"""
    print("\n🤖 AI训练演示")
    print("="*50)
    
    trainer = AITrainer()
    
    print("开始训练AI...")
    for episode in range(10):
        red_score, blue_score, turns = trainer.train_episode()
        
        if episode % 2 == 0:
            stats = trainer.get_training_stats()
            print(f"回合 {episode + 1}: 红队{red_score} vs 蓝队{blue_score} "
                  f"(用时{turns}回合, 探索率{stats['red_epsilon']:.2f})")
    
    print("\n训练完成！进行一场演示对战...")
    
    # 进行一场演示游戏
    final_state, history = trainer.play_game()
    
    print(f"演示对战结果:")
    print(f"总回合数: {len(history)}")
    
    final_info = history[-1]
    print(f"最终得分: 红队{final_info['red_score']} vs 蓝队{final_info['blue_score']}")
    
    if final_info['red_score'] > final_info['blue_score']:
        print("🔴 红队获胜！")
    elif final_info['blue_score'] > final_info['red_score']:
        print("🔵 蓝队获胜！")
    else:
        print("⚪ 平局！")

def demo_energy_visualization():
    """演示能量可视化"""
    print("\n🌈 能量可视化演示")
    print("="*50)
    
    # 创建一个测试网格
    test_grid = np.zeros((10, 10))
    
    # 创建一些能量模式
    test_grid[2:4, 2:6] = 8.0    # 红色区域
    test_grid[6:8, 4:8] = -7.0   # 蓝色区域
    test_grid[1, 1] = 3.0        # 正能量点
    test_grid[8, 8] = -3.0       # 负能量点
    
    print("能量分布示例 (10x10):")
    print("🔴 = 强红色能量 (>5)")
    print("🔵 = 强蓝色能量 (<-5)")
    print("🟡 = 正能量 (0-5)")
    print("🟦 = 负能量 (-5-0)")
    print("⚪ = 中性 (0)")
    
    # 显示网格
    display_grid = np.full((10, 10), "  ", dtype=object)
    
    for y in range(10):
        for x in range(10):
            energy = test_grid[y, x]
            if energy > 5:
                display_grid[y, x] = "🔴"
            elif energy < -5:
                display_grid[y, x] = "🔵"
            elif energy > 0:
                display_grid[y, x] = "🟡"
            elif energy < 0:
                display_grid[y, x] = "🟦"
            else:
                display_grid[y, x] = "⚪"
    
    print("\n   ", end="")
    for x in range(10):
        print(f"{x:2}", end="")
    print()
    
    for y in range(10):
        print(f"{y:2} ", end="")
        for x in range(10):
            print(display_grid[y, x], end="")
        print()

def main():
    """主演示函数"""
    print("🔮 晶格信使 (The Lattice Messengers) 演示")
    print("AI进化模拟游戏")
    print("="*60)
    
    try:
        # 基本游戏演示
        demo_basic_game()
        
        # AI训练演示
        demo_ai_training()
        
        # 能量可视化演示
        demo_energy_visualization()
        
        print("\n✨ 所有演示完成！")
        print("运行 'python main.py' 启动完整的Web界面游戏")
        
    except KeyboardInterrupt:
        print("\n演示被用户中断")
    except Exception as e:
        print(f"\n演示过程中出现错误: {e}")

if __name__ == "__main__":
    main()
