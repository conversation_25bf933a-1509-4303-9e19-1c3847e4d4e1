import numpy as np
import random
from typing import List, Tu<PERSON>, Dict, Optional
from enum import Enum
import config

class Team(Enum):
    RED = 1
    BLUE = -1

class Action(Enum):
    MOVE_UP = 0
    MOVE_DOWN = 1
    MOVE_LEFT = 2
    MOVE_RIGHT = 3
    CHANT = 4

class Messenger:
    def __init__(self, x: int, y: int, team: Team, messenger_id: int):
        self.x = x
        self.y = y
        self.team = team
        self.id = messenger_id
        self.energy_spent = 0.0
        
    def get_position(self) -> Tuple[int, int]:
        return (self.x, self.y)
    
    def move(self, action: Action, grid_size: int) -> bool:
        """移动信使，返回是否成功移动"""
        new_x, new_y = self.x, self.y
        
        if action == Action.MOVE_UP and self.y > 0:
            new_y = self.y - 1
        elif action == Action.MOVE_DOWN and self.y < grid_size - 1:
            new_y = self.y + 1
        elif action == Action.MOVE_LEFT and self.x > 0:
            new_x = self.x - 1
        elif action == Action.MOVE_RIGHT and self.x < grid_size - 1:
            new_x = self.x + 1
        else:
            return False
            
        self.x, self.y = new_x, new_y
        self.energy_spent += config.MOVE_COST
        return True

class GameState:
    def __init__(self, grid_size: int = None):
        self.grid_size = grid_size or config.GRID_SIZE
        self.energy_grid = np.zeros((self.grid_size, self.grid_size), dtype=float)
        self.turn = 0
        self.max_turns = config.MAX_TURNS

        # 初始化信使位置
        self.red_messengers = []
        for i, (x, y) in enumerate(config.RED_INITIAL_POSITIONS):
            self.red_messengers.append(Messenger(x, y, Team.RED, i))

        self.blue_messengers = []
        for i, (x, y) in enumerate(config.BLUE_INITIAL_POSITIONS):
            self.blue_messengers.append(Messenger(x, y, Team.BLUE, i))

        self.all_messengers = self.red_messengers + self.blue_messengers
        
    def get_state_vector(self) -> np.ndarray:
        """获取游戏状态向量用于AI输入"""
        # 能量网格 (20x20 = 400)
        grid_flat = self.energy_grid.flatten()
        
        # 信使位置 (6个信使 x 2坐标 = 12)
        messenger_positions = []
        for messenger in self.all_messengers:
            messenger_positions.extend([messenger.x / self.grid_size, messenger.y / self.grid_size])
        
        # 回合信息 (1)
        turn_info = [self.turn / self.max_turns]
        
        return np.concatenate([grid_flat, messenger_positions, turn_info])
    
    def execute_action(self, messenger: Messenger, action: Action) -> float:
        """执行动作并返回奖励"""
        reward = 0.0
        
        if action == Action.CHANT:
            # 咏唱：改变当前位置的能量
            energy_change = config.ENERGY_CHANGE_PER_CHANT if messenger.team == Team.RED else -config.ENERGY_CHANGE_PER_CHANT
            old_energy = self.energy_grid[messenger.y, messenger.x]
            new_energy = np.clip(old_energy + energy_change, config.MIN_ENERGY, config.MAX_ENERGY)
            self.energy_grid[messenger.y, messenger.x] = new_energy
            reward = -config.CHANT_COST
            messenger.energy_spent += config.CHANT_COST
        else:
            # 移动
            if messenger.move(action, self.grid_size):
                reward = -config.MOVE_COST
            else:
                reward = -config.INVALID_MOVE_PENALTY  # 无效移动的惩罚
                
        return reward
    
    def calculate_resonance_chains(self) -> Tuple[int, int]:
        """计算红蓝两队的最长共鸣链"""
        red_score = self._find_longest_chain(lambda x: x > config.RED_RESONANCE_THRESHOLD)
        blue_score = self._find_longest_chain(lambda x: x < config.BLUE_RESONANCE_THRESHOLD)
        return red_score, blue_score
    
    def _find_longest_chain(self, condition) -> int:
        """使用DFS查找满足条件的最长连通链"""
        visited = np.zeros((self.grid_size, self.grid_size), dtype=bool)
        max_length = 0
        
        for i in range(self.grid_size):
            for j in range(self.grid_size):
                if not visited[i, j] and condition(self.energy_grid[i, j]):
                    length = self._dfs_chain_length(i, j, visited, condition)
                    max_length = max(max_length, length)
        
        return max_length ** 2  # 返回长度的平方
    
    def _dfs_chain_length(self, x: int, y: int, visited: np.ndarray, condition) -> int:
        """深度优先搜索计算连通链长度"""
        if (x < 0 or x >= self.grid_size or y < 0 or y >= self.grid_size or 
            visited[x, y] or not condition(self.energy_grid[x, y])):
            return 0
        
        visited[x, y] = True
        length = 1
        
        # 检查四个方向
        directions = [(0, 1), (0, -1), (1, 0), (-1, 0)]
        for dx, dy in directions:
            length += self._dfs_chain_length(x + dx, y + dy, visited, condition)
        
        return length
    
    def is_game_over(self) -> bool:
        """检查游戏是否结束"""
        return self.turn >= self.max_turns
    
    def get_final_scores(self) -> Tuple[int, int]:
        """获取最终得分"""
        return self.calculate_resonance_chains()
    
    def advance_turn(self):
        """推进回合"""
        self.turn += 1
    
    def get_game_info(self) -> Dict:
        """获取游戏信息用于前端显示"""
        red_score, blue_score = self.calculate_resonance_chains()
        
        return {
            'turn': self.turn,
            'max_turns': self.max_turns,
            'energy_grid': self.energy_grid.tolist(),
            'red_messengers': [{'x': m.x, 'y': m.y, 'id': m.id} for m in self.red_messengers],
            'blue_messengers': [{'x': m.x, 'y': m.y, 'id': m.id} for m in self.blue_messengers],
            'red_score': red_score,
            'blue_score': blue_score,
            'game_over': self.is_game_over()
        }

class LatticeMessengersGame:
    def __init__(self):
        self.reset()
    
    def reset(self) -> GameState:
        """重置游戏"""
        self.state = GameState()
        return self.state
    
    def step(self, red_actions: List[Action], blue_actions: List[Action]) -> Tuple[GameState, List[float], bool]:
        """执行一步游戏，返回新状态、奖励和是否结束"""
        rewards = []
        
        # 执行红队动作
        for i, action in enumerate(red_actions):
            reward = self.state.execute_action(self.state.red_messengers[i], action)
            rewards.append(reward)
        
        # 执行蓝队动作
        for i, action in enumerate(blue_actions):
            reward = self.state.execute_action(self.state.blue_messengers[i], action)
            rewards.append(reward)
        
        self.state.advance_turn()
        
        return self.state, rewards, self.state.is_game_over()
