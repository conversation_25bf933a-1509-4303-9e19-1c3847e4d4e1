#!/bin/bash

# 设置颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 打印横幅
echo -e "${BLUE}"
echo "╔══════════════════════════════════════════════════════════════╗"
echo "║                                                              ║"
echo "║                    🔮 晶格信使 🔮                              ║"
echo "║                The Lattice Messengers                        ║"
echo "║                                                              ║"
echo "║                   AI进化模拟游戏                              ║"
echo "║                                                              ║"
echo "╚══════════════════════════════════════════════════════════════╝"
echo -e "${NC}"

# 检查Python环境
echo -e "${YELLOW}正在检查Python环境...${NC}"
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo -e "${RED}❌ 错误: 未找到Python环境${NC}"
        echo "请先安装Python 3.8或更高版本"
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

echo -e "${GREEN}✅ Python环境检查通过${NC}"

# 检查pip
if ! command -v pip3 &> /dev/null; then
    if ! command -v pip &> /dev/null; then
        echo -e "${RED}❌ 错误: 未找到pip${NC}"
        exit 1
    else
        PIP_CMD="pip"
    fi
else
    PIP_CMD="pip3"
fi

# 检查依赖包
echo -e "${YELLOW}正在检查依赖包...${NC}"
if ! $PIP_CMD show torch &> /dev/null; then
    echo -e "${YELLOW}📦 正在安装依赖包...${NC}"
    $PIP_CMD install -r requirements.txt
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ 依赖包安装失败${NC}"
        exit 1
    fi
else
    echo -e "${GREEN}✅ 依赖包检查通过${NC}"
fi

echo ""
echo -e "${GREEN}🚀 正在启动晶格信使游戏服务器...${NC}"
echo ""
echo -e "${BLUE}游戏界面: http://localhost:8000${NC}"
echo -e "${YELLOW}按 Ctrl+C 停止服务器${NC}"
echo ""

# 启动游戏
$PYTHON_CMD main.py

echo ""
echo -e "${GREEN}游戏服务器已关闭${NC}"
