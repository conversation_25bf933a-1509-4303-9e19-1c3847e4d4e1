class LatticeMessengersUI {
    constructor() {
        this.websocket = null;
        this.isConnected = false;
        this.gridSize = 20;
        this.currentGameState = null;
        
        this.initializeGrid();
        this.connectWebSocket();
    }
    
    initializeGrid() {
        const grid = document.getElementById('energy-grid');
        grid.innerHTML = '';
        
        for (let i = 0; i < this.gridSize * this.gridSize; i++) {
            const cell = document.createElement('div');
            cell.className = 'grid-cell';
            cell.id = `cell-${i}`;
            grid.appendChild(cell);
        }
    }
    
    connectWebSocket() {
        try {
            this.websocket = new WebSocket('ws://localhost:8765');
            
            this.websocket.onopen = () => {
                this.isConnected = true;
                this.updateConnectionStatus(true);
                console.log('WebSocket连接已建立');
            };
            
            this.websocket.onmessage = (event) => {
                const message = JSON.parse(event.data);
                this.handleMessage(message);
            };
            
            this.websocket.onclose = () => {
                this.isConnected = false;
                this.updateConnectionStatus(false);
                console.log('WebSocket连接已关闭');
                
                // 尝试重连
                setTimeout(() => {
                    if (!this.isConnected) {
                        this.connectWebSocket();
                    }
                }, 3000);
            };
            
            this.websocket.onerror = (error) => {
                console.error('WebSocket错误:', error);
                this.updateConnectionStatus(false);
            };
            
        } catch (error) {
            console.error('连接WebSocket失败:', error);
            this.updateConnectionStatus(false);
        }
    }
    
    updateConnectionStatus(connected) {
        const statusElement = document.getElementById('connection-status');
        const statusText = document.getElementById('status-text');
        
        if (connected) {
            statusElement.className = 'status connected';
            statusText.textContent = '✅ 已连接到服务器';
        } else {
            statusElement.className = 'status disconnected';
            statusText.textContent = '❌ 连接断开';
        }
    }
    
    handleMessage(message) {
        switch (message.type) {
            case 'training_stats':
                this.updateTrainingStats(message.data);
                break;
            case 'training_status':
                this.updateTrainingStatus(message.data.status);
                break;
            case 'game_state':
                this.updateGameState(message.data);
                break;
            case 'game_status':
                this.updateGameStatus(message.data.status);
                break;
            case 'training_result':
                console.log('训练结果:', message.data);
                break;
            case 'error':
                console.error('服务器错误:', message.data.message);
                break;
            default:
                console.log('未知消息类型:', message.type);
        }
    }
    
    updateTrainingStats(stats) {
        document.getElementById('episodes').textContent = stats.episodes;
        document.getElementById('red-wins').textContent = stats.red_wins;
        document.getElementById('blue-wins').textContent = stats.blue_wins;
        document.getElementById('draws').textContent = stats.draws;
        document.getElementById('epsilon').textContent = 
            Math.round(stats.red_epsilon * 100) + '%';
    }
    
    updateTrainingStatus(status) {
        const startBtn = document.getElementById('start-training');
        const stopBtn = document.getElementById('stop-training');
        
        if (status === 'started') {
            startBtn.disabled = true;
            stopBtn.disabled = false;
        } else if (status === 'stopped') {
            startBtn.disabled = false;
            stopBtn.disabled = true;
        }
    }
    
    updateGameState(gameState) {
        this.currentGameState = gameState;
        
        // 更新回合信息
        document.getElementById('turn-info').textContent = 
            `${gameState.turn} / ${gameState.max_turns}`;
        
        const progress = (gameState.turn / gameState.max_turns) * 100;
        document.getElementById('turn-progress').style.width = progress + '%';
        
        // 更新得分
        document.getElementById('red-score').textContent = gameState.red_score;
        document.getElementById('blue-score').textContent = gameState.blue_score;
        
        // 更新游戏状态
        if (gameState.game_over) {
            const winner = gameState.red_score > gameState.blue_score ? '红队胜利!' :
                          gameState.blue_score > gameState.red_score ? '蓝队胜利!' : '平局!';
            document.getElementById('game-status').textContent = winner;
        } else {
            document.getElementById('game-status').textContent = '对战进行中';
        }
        
        // 更新网格
        this.updateGrid(gameState);
    }
    
    updateGameStatus(status) {
        const gameStatusElement = document.getElementById('game-status');
        
        switch (status) {
            case 'starting':
                gameStatusElement.textContent = '游戏开始...';
                break;
            case 'finished':
                gameStatusElement.textContent = '游戏结束';
                break;
        }
    }
    
    updateGrid(gameState) {
        // 清除所有信使
        document.querySelectorAll('.messenger').forEach(el => el.remove());
        
        // 更新能量网格
        for (let y = 0; y < this.gridSize; y++) {
            for (let x = 0; x < this.gridSize; x++) {
                const cellIndex = y * this.gridSize + x;
                const cell = document.getElementById(`cell-${cellIndex}`);
                const energy = gameState.energy_grid[y][x];
                
                // 设置背景颜色
                cell.style.backgroundColor = this.getEnergyColor(energy);
                
                // 设置工具提示
                cell.title = `位置: (${x}, ${y}), 能量: ${energy.toFixed(1)}`;
            }
        }
        
        // 添加红队信使
        gameState.red_messengers.forEach(messenger => {
            this.addMessenger(messenger.x, messenger.y, 'red', messenger.id);
        });
        
        // 添加蓝队信使
        gameState.blue_messengers.forEach(messenger => {
            this.addMessenger(messenger.x, messenger.y, 'blue', messenger.id);
        });
    }
    
    getEnergyColor(energy) {
        // 将能量值 (-10 到 +10) 映射到颜色
        const normalized = (energy + 10) / 20; // 归一化到 0-1
        
        if (normalized < 0.5) {
            // 蓝色范围 (深蓝到灰色)
            const intensity = normalized * 2; // 0-1
            const blue = Math.round(128 + intensity * 127); // 128-255
            const other = Math.round(intensity * 128); // 0-128
            return `rgb(${other}, ${other}, ${blue})`;
        } else {
            // 红色范围 (灰色到亮红)
            const intensity = (normalized - 0.5) * 2; // 0-1
            const red = Math.round(128 + intensity * 127); // 128-255
            const other = Math.round(128 - intensity * 128); // 128-0
            return `rgb(${red}, ${other}, ${other})`;
        }
    }
    
    addMessenger(x, y, team, id) {
        const cellIndex = y * this.gridSize + x;
        const cell = document.getElementById(`cell-${cellIndex}`);
        
        const messenger = document.createElement('div');
        messenger.className = `messenger ${team}`;
        messenger.title = `${team === 'red' ? '红队' : '蓝队'}信使 #${id}`;
        
        cell.appendChild(messenger);
    }
    
    sendMessage(message) {
        if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
            this.websocket.send(JSON.stringify(message));
        } else {
            console.error('WebSocket未连接');
        }
    }
}

// 全局UI实例
let gameUI;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    gameUI = new LatticeMessengersUI();
});

// 控制函数
function startTraining() {
    gameUI.sendMessage({ type: 'start_training' });
}

function stopTraining() {
    gameUI.sendMessage({ type: 'stop_training' });
}

function playGame() {
    gameUI.sendMessage({ type: 'play_game' });
}

// 键盘快捷键
document.addEventListener('keydown', (event) => {
    switch (event.key) {
        case 's':
        case 'S':
            if (!document.getElementById('start-training').disabled) {
                startTraining();
            }
            break;
        case 'x':
        case 'X':
            if (!document.getElementById('stop-training').disabled) {
                stopTraining();
            }
            break;
        case 'p':
        case 'P':
            playGame();
            break;
    }
});
