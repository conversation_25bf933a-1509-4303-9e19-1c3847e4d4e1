import asyncio
import json
import logging
import threading
import time
from typing import Dict, List
import websockets
from websockets.server import WebSocketServerProtocol
from http.server import HTTPServer, SimpleHTTPRequestHandler
import os
from ai_model import AITrainer
import config

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class GameServer:
    def __init__(self):
        self.trainer = AITrainer()
        self.connected_clients = set()
        self.is_training = False
        self.is_playing = False
        self.current_game_history = []
        self.training_thread = None
        
    async def register_client(self, websocket: WebSocketServerProtocol):
        """注册新客户端"""
        self.connected_clients.add(websocket)
        logger.info(f"客户端已连接，当前连接数: {len(self.connected_clients)}")
        
        # 发送当前状态
        await self.send_training_stats(websocket)
        
    async def unregister_client(self, websocket: WebSocketServerProtocol):
        """注销客户端"""
        self.connected_clients.discard(websocket)
        logger.info(f"客户端已断开，当前连接数: {len(self.connected_clients)}")
        
    async def broadcast_message(self, message: dict):
        """广播消息给所有客户端"""
        if self.connected_clients:
            message_str = json.dumps(message, ensure_ascii=False)
            disconnected = set()
            
            for client in self.connected_clients:
                try:
                    await client.send(message_str)
                except websockets.exceptions.ConnectionClosed:
                    disconnected.add(client)
                except Exception as e:
                    logger.error(f"发送消息失败: {e}")
                    disconnected.add(client)
            
            # 移除断开的连接
            self.connected_clients -= disconnected
    
    async def send_training_stats(self, websocket: WebSocketServerProtocol = None):
        """发送训练统计信息"""
        stats = self.trainer.get_training_stats()
        message = {
            'type': 'training_stats',
            'data': stats
        }
        
        if websocket:
            try:
                await websocket.send(json.dumps(message, ensure_ascii=False))
            except:
                pass
        else:
            await self.broadcast_message(message)
    
    async def handle_message(self, websocket: WebSocketServerProtocol, message: str):
        """处理客户端消息"""
        try:
            data = json.loads(message)
            message_type = data.get('type')
            
            if message_type == 'start_training':
                await self.start_training()
            elif message_type == 'stop_training':
                await self.stop_training()
            elif message_type == 'play_game':
                await self.play_demonstration_game()
            elif message_type == 'get_stats':
                await self.send_training_stats()
            else:
                logger.warning(f"未知消息类型: {message_type}")
                
        except json.JSONDecodeError:
            logger.error("无效的JSON消息")
        except Exception as e:
            logger.error(f"处理消息时出错: {e}")
    
    async def start_training(self):
        """开始训练"""
        if self.is_training:
            return
            
        self.is_training = True
        await self.broadcast_message({
            'type': 'training_status',
            'data': {'status': 'started'}
        })
        
        # 在后台线程中运行训练
        self.training_thread = threading.Thread(target=self._training_loop)
        self.training_thread.daemon = True
        self.training_thread.start()
    
    async def stop_training(self):
        """停止训练"""
        self.is_training = False
        await self.broadcast_message({
            'type': 'training_status',
            'data': {'status': 'stopped'}
        })
    
    def _training_loop(self):
        """训练循环（在后台线程中运行）"""
        episode_count = 0
        
        while self.is_training:
            try:
                # 训练一个回合
                red_score, blue_score, turns = self.trainer.train_episode()
                episode_count += 1
                
                # 每N个回合发送一次统计信息
                if episode_count % config.UPDATE_FREQUENCY == 0:
                    asyncio.run_coroutine_threadsafe(
                        self.send_training_stats(),
                        asyncio.get_event_loop()
                    )

                # 每N个回合发送一次训练结果
                if episode_count % config.RESULT_FREQUENCY == 0:
                    message = {
                        'type': 'training_result',
                        'data': {
                            'episode': episode_count,
                            'red_score': red_score,
                            'blue_score': blue_score,
                            'turns': turns
                        }
                    }
                    asyncio.run_coroutine_threadsafe(
                        self.broadcast_message(message),
                        asyncio.get_event_loop()
                    )
                
                # 短暂休息避免CPU过载
                time.sleep(0.01)
                
            except Exception as e:
                logger.error(f"训练过程中出错: {e}")
                break
    
    async def play_demonstration_game(self):
        """进行演示游戏"""
        if self.is_playing:
            return
            
        self.is_playing = True
        
        try:
            await self.broadcast_message({
                'type': 'game_status',
                'data': {'status': 'starting'}
            })
            
            # 进行游戏
            final_state, history = self.trainer.play_game()
            self.current_game_history = history
            
            # 逐步发送游戏历史
            for i, game_state in enumerate(history):
                await self.broadcast_message({
                    'type': 'game_state',
                    'data': game_state
                })
                
                # 控制播放速度
                if i < len(history) - 1:  # 不在最后一帧暂停
                    await asyncio.sleep(config.ANIMATION_SPEED)
            
            await self.broadcast_message({
                'type': 'game_status',
                'data': {'status': 'finished'}
            })
            
        except Exception as e:
            logger.error(f"演示游戏时出错: {e}")
            await self.broadcast_message({
                'type': 'error',
                'data': {'message': str(e)}
            })
        finally:
            self.is_playing = False

# 全局游戏服务器实例
game_server = GameServer()

async def websocket_handler(websocket: WebSocketServerProtocol, path: str):
    """WebSocket处理器"""
    await game_server.register_client(websocket)
    
    try:
        async for message in websocket:
            await game_server.handle_message(websocket, message)
    except websockets.exceptions.ConnectionClosed:
        pass
    except Exception as e:
        logger.error(f"WebSocket错误: {e}")
    finally:
        await game_server.unregister_client(websocket)

class CustomHTTPRequestHandler(SimpleHTTPRequestHandler):
    """自定义HTTP请求处理器"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=os.getcwd(), **kwargs)
    
    def end_headers(self):
        self.send_header('Cache-Control', 'no-cache, no-store, must-revalidate')
        self.send_header('Pragma', 'no-cache')
        self.send_header('Expires', '0')
        super().end_headers()

def start_http_server(port: int = None):
    """启动HTTP服务器"""
    if port is None:
        port = config.HTTP_PORT
    server = HTTPServer(('localhost', port), CustomHTTPRequestHandler)
    logger.info(f"HTTP服务器启动在 http://localhost:{port}")
    server.serve_forever()

async def start_websocket_server(port: int = None):
    """启动WebSocket服务器"""
    if port is None:
        port = config.WEBSOCKET_PORT
    logger.info(f"WebSocket服务器启动在 ws://localhost:{port}")

    # 启动WebSocket服务器
    start_server = websockets.serve(websocket_handler, 'localhost', port)
    await start_server
    logger.info("WebSocket服务器已启动，等待连接...")

    # 保持服务器运行
    try:
        await asyncio.Future()  # 永远等待
    except asyncio.CancelledError:
        logger.info("WebSocket服务器被取消")

async def main_async():
    """异步主函数"""
    # 在后台线程启动HTTP服务器
    http_thread = threading.Thread(target=start_http_server, args=(config.HTTP_PORT,))
    http_thread.daemon = True
    http_thread.start()

    # 启动WebSocket服务器
    await start_websocket_server(config.WEBSOCKET_PORT)

def main():
    """主函数"""
    try:
        asyncio.run(main_async())
    except KeyboardInterrupt:
        logger.info("服务器被用户中断")
    except Exception as e:
        logger.error(f"服务器启动失败: {e}")

if __name__ == "__main__":
    main()
