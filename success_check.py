#!/usr/bin/env python3
"""
晶格信使成功验证脚本
确认所有功能都正常工作
"""

import sys
import asyncio
import time

def print_banner():
    """打印成功横幅"""
    print("🎉" * 20)
    print("🔮 晶格信使 - 成功验证 🔮")
    print("🎉" * 20)
    print()

def check_dependencies():
    """检查依赖包"""
    print("📦 检查依赖包...")
    
    deps = [
        ('torch', 'PyTorch深度学习框架'),
        ('numpy', 'NumPy数值计算库'),
        ('websockets', 'WebSocket通信库')
    ]
    
    all_ok = True
    for module, desc in deps:
        try:
            __import__(module)
            print(f"  ✅ {desc}")
        except ImportError:
            print(f"  ❌ {desc} - 未安装")
            all_ok = False
    
    return all_ok

def check_core_modules():
    """检查核心模块"""
    print("\n🎮 检查游戏核心模块...")
    
    modules = [
        ('config', '配置管理'),
        ('game_engine', '游戏引擎'),
        ('ai_model', 'AI模型'),
        ('web_server', 'Web服务器'),
        ('web_server_simple', '简化服务器')
    ]
    
    all_ok = True
    for module, desc in modules:
        try:
            __import__(module)
            print(f"  ✅ {desc}")
        except ImportError as e:
            print(f"  ❌ {desc} - {e}")
            all_ok = False
    
    return all_ok

def check_websocket_fix():
    """检查WebSocket修复"""
    print("\n🔗 检查WebSocket修复状态...")
    
    try:
        from web_server import websocket_handler
        import inspect
        
        sig = inspect.signature(websocket_handler)
        params = list(sig.parameters.keys())
        
        if len(params) == 1 and params[0] == 'websocket':
            print("  ✅ WebSocket处理器签名正确")
            return True
        else:
            print(f"  ❌ WebSocket处理器签名错误: {params}")
            return False
            
    except Exception as e:
        print(f"  ❌ WebSocket检查失败: {e}")
        return False

def check_game_functionality():
    """检查游戏功能"""
    print("\n🎯 检查游戏核心功能...")
    
    try:
        from game_engine import LatticeMessengersGame, Action
        from ai_model import AITrainer
        
        # 测试游戏引擎
        game = LatticeMessengersGame()
        state = game.reset()
        print("  ✅ 游戏引擎初始化成功")
        
        # 测试AI系统
        trainer = AITrainer()
        stats = trainer.get_training_stats()
        print("  ✅ AI训练系统正常")
        
        # 测试游戏步骤
        red_actions = [Action.CHANT, Action.MOVE_RIGHT, Action.MOVE_DOWN]
        blue_actions = [Action.CHANT, Action.MOVE_LEFT, Action.MOVE_UP]
        
        state, rewards, done = game.step(red_actions, blue_actions)
        print("  ✅ 游戏逻辑运行正常")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 游戏功能测试失败: {e}")
        return False

async def check_websocket_server():
    """检查WebSocket服务器"""
    print("\n🚀 检查WebSocket服务器启动...")
    
    try:
        from web_server_simple import start_websocket_server
        
        # 尝试启动服务器
        server_task = asyncio.create_task(start_websocket_server(8768))
        
        # 等待短时间
        await asyncio.sleep(0.5)
        
        if not server_task.done():
            print("  ✅ WebSocket服务器启动成功")
            server_task.cancel()
            try:
                await server_task
            except asyncio.CancelledError:
                pass
            return True
        else:
            print("  ❌ WebSocket服务器启动失败")
            return False
            
    except Exception as e:
        print(f"  ❌ WebSocket服务器测试失败: {e}")
        return False

def show_success_message():
    """显示成功消息"""
    print("\n" + "🎉" * 30)
    print("🏆 恭喜！晶格信使已完全就绪！")
    print("🎉" * 30)
    print()
    print("🚀 现在您可以:")
    print("   1. 运行 'python main.py' 启动完整游戏")
    print("   2. 访问 http://localhost:8000 查看Web界面")
    print("   3. 点击'开始训练'让AI学习对战")
    print("   4. 点击'观看对战'查看AI实时对战")
    print()
    print("🎮 游戏特色:")
    print("   • 20x20能量网格世界")
    print("   • 红蓝两队AI智能体对战")
    print("   • 实时可视化界面")
    print("   • 深度强化学习AI")
    print()
    print("💡 如需帮助，查看:")
    print("   • README.md - 详细说明")
    print("   • QUICK_START.md - 快速开始")
    print("   • offline_demo.py - 离线演示")
    print()
    print("🔮 享受AI进化的奇妙旅程！")

def show_failure_message(failed_checks):
    """显示失败消息"""
    print("\n" + "⚠️" * 30)
    print("❌ 发现一些问题需要解决")
    print("⚠️" * 30)
    print()
    print("失败的检查项:")
    for check in failed_checks:
        print(f"   • {check}")
    print()
    print("💡 建议解决方案:")
    print("   1. 运行 'pip install -r requirements.txt' 安装依赖")
    print("   2. 运行 'python test_websocket_fix.py' 检查WebSocket")
    print("   3. 运行 'python offline_demo.py' 体验离线版本")
    print("   4. 查看 INSTALL_GUIDE.md 获取详细帮助")

async def main():
    """主验证函数"""
    print_banner()
    
    checks = []
    failed_checks = []
    
    # 依赖检查
    if check_dependencies():
        checks.append("依赖包")
    else:
        failed_checks.append("依赖包缺失")
    
    # 核心模块检查
    if check_core_modules():
        checks.append("核心模块")
    else:
        failed_checks.append("核心模块问题")
    
    # WebSocket修复检查
    if check_websocket_fix():
        checks.append("WebSocket修复")
    else:
        failed_checks.append("WebSocket未修复")
    
    # 游戏功能检查
    if check_game_functionality():
        checks.append("游戏功能")
    else:
        failed_checks.append("游戏功能异常")
    
    # WebSocket服务器检查
    if await check_websocket_server():
        checks.append("WebSocket服务器")
    else:
        failed_checks.append("WebSocket服务器问题")
    
    # 结果总结
    print(f"\n📊 检查结果: {len(checks)}/{len(checks) + len(failed_checks)} 项通过")
    
    if not failed_checks:
        show_success_message()
        return True
    else:
        show_failure_message(failed_checks)
        return False

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n\n⏹️ 验证被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 验证过程中出现错误: {e}")
        sys.exit(1)
