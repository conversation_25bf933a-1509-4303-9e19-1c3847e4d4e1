#!/usr/bin/env python3
"""
测试WebSocket修复是否成功
"""

import asyncio
import json
import sys
import time
import threading

def test_websocket_handler_signature():
    """测试WebSocket处理器函数签名"""
    print("🔍 测试WebSocket处理器函数签名...")
    
    try:
        from web_server import websocket_handler
        import inspect
        
        # 获取函数签名
        sig = inspect.signature(websocket_handler)
        params = list(sig.parameters.keys())
        
        print(f"  web_server.websocket_handler 参数: {params}")
        
        if len(params) == 1 and params[0] == 'websocket':
            print("  ✅ web_server.py 函数签名正确")
            return True
        else:
            print(f"  ❌ web_server.py 函数签名错误，期望['websocket']，实际{params}")
            return False
            
    except Exception as e:
        print(f"  ❌ 测试web_server失败: {e}")
        return False

def test_simple_websocket_handler_signature():
    """测试简化WebSocket处理器函数签名"""
    print("\n🔍 测试简化WebSocket处理器函数签名...")
    
    try:
        from web_server_simple import websocket_handler
        import inspect
        
        # 获取函数签名
        sig = inspect.signature(websocket_handler)
        params = list(sig.parameters.keys())
        
        print(f"  web_server_simple.websocket_handler 参数: {params}")
        
        if len(params) == 1 and params[0] == 'websocket':
            print("  ✅ web_server_simple.py 函数签名正确")
            return True
        else:
            print(f"  ❌ web_server_simple.py 函数签名错误，期望['websocket']，实际{params}")
            return False
            
    except Exception as e:
        print(f"  ❌ 测试web_server_simple失败: {e}")
        return False

def test_websockets_version():
    """测试websockets库版本"""
    print("\n📦 检查websockets库版本...")
    
    try:
        import websockets
        version = getattr(websockets, '__version__', 'unknown')
        print(f"  websockets版本: {version}")
        
        # 解析版本号
        if version != 'unknown':
            major_version = int(version.split('.')[0])
            if major_version >= 10:
                print("  ✅ 使用新版websockets库 (>=10.0)")
                return 'new'
            else:
                print("  ⚠️ 使用旧版websockets库 (<10.0)")
                return 'old'
        else:
            print("  ❌ 无法确定版本")
            return 'unknown'
            
    except Exception as e:
        print(f"  ❌ 检查版本失败: {e}")
        return 'error'

async def test_websocket_server_start():
    """测试WebSocket服务器启动"""
    print("\n🚀 测试WebSocket服务器启动...")
    
    try:
        from web_server_simple import start_websocket_server
        
        # 创建一个测试任务
        server_task = asyncio.create_task(start_websocket_server(8766))
        
        # 等待一小段时间看是否有错误
        await asyncio.sleep(1)
        
        if not server_task.done():
            print("  ✅ WebSocket服务器启动成功")
            server_task.cancel()
            try:
                await server_task
            except asyncio.CancelledError:
                pass
            return True
        else:
            # 服务器任务已完成，可能有错误
            try:
                await server_task
            except Exception as e:
                print(f"  ❌ WebSocket服务器启动失败: {e}")
                return False
            
    except Exception as e:
        print(f"  ❌ 测试服务器启动失败: {e}")
        return False

async def test_websocket_connection():
    """测试WebSocket连接"""
    print("\n🔗 测试WebSocket连接...")
    
    try:
        import websockets
        
        # 启动测试服务器
        from web_server_simple import websocket_handler
        
        async def test_handler(websocket):
            await websocket.send("test_response")
        
        # 在测试端口启动服务器
        server = await websockets.serve(test_handler, 'localhost', 8767)
        
        try:
            # 连接到测试服务器
            async with websockets.connect('ws://localhost:8767') as websocket:
                await websocket.send("test_message")
                response = await websocket.recv()
                
                if response == "test_response":
                    print("  ✅ WebSocket连接测试成功")
                    return True
                else:
                    print(f"  ❌ 响应错误: {response}")
                    return False
                    
        finally:
            server.close()
            await server.wait_closed()
            
    except Exception as e:
        print(f"  ❌ WebSocket连接测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🔮 WebSocket修复验证测试")
    print("="*50)
    
    results = []
    
    # 测试函数签名
    results.append(test_websocket_handler_signature())
    results.append(test_simple_websocket_handler_signature())
    
    # 测试版本
    version_result = test_websockets_version()
    results.append(version_result not in ['error', 'unknown'])
    
    # 测试服务器启动
    server_result = await test_websocket_server_start()
    results.append(server_result)
    
    # 测试连接
    connection_result = await test_websocket_connection()
    results.append(connection_result)
    
    # 总结
    print("\n" + "="*50)
    print("📊 测试结果总结")
    print("="*50)
    
    passed = sum(results)
    total = len(results)
    
    print(f"通过测试: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过！WebSocket问题已修复")
        print("\n✅ 现在可以安全使用:")
        print("   python main.py")
        print("   python start_simple.py")
        return True
    else:
        print("⚠️ 部分测试失败，可能仍有问题")
        print("\n💡 建议使用备用方案:")
        print("   python offline_demo.py")
        return False

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n\n⏹️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        sys.exit(1)
