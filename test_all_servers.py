#!/usr/bin/env python3
"""
测试所有服务器启动方式
验证哪种方式在当前环境下工作正常
"""

import sys
import asyncio
import time
import subprocess
import threading
from typing import List, <PERSON><PERSON>

def test_imports() -> bool:
    """测试基础模块导入"""
    print("🔍 测试基础模块导入...")
    
    modules = [
        ('torch', 'PyTorch'),
        ('numpy', 'NumPy'),
        ('websockets', 'WebSockets'),
        ('game_engine', '游戏引擎'),
        ('ai_model', 'AI模型'),
        ('config', '配置文件')
    ]
    
    success = True
    for module, name in modules:
        try:
            __import__(module)
            print(f"  ✅ {name}")
        except ImportError as e:
            print(f"  ❌ {name}: {e}")
            success = False
    
    return success

def test_server_import(server_module: str) -> bool:
    """测试服务器模块导入"""
    try:
        module = __import__(server_module)
        return hasattr(module, 'main')
    except ImportError:
        return False

def test_websocket_compatibility() -> List[str]:
    """测试WebSocket兼容性"""
    print("\n🔗 测试WebSocket兼容性...")
    
    working_servers = []
    
    # 测试简化服务器
    if test_server_import('web_server_simple'):
        print("  ✅ 简化服务器 (web_server_simple.py)")
        working_servers.append('简化服务器')
    else:
        print("  ❌ 简化服务器导入失败")
    
    # 测试标准服务器
    if test_server_import('web_server'):
        print("  ✅ 标准服务器 (web_server.py)")
        working_servers.append('标准服务器')
    else:
        print("  ❌ 标准服务器导入失败")
    
    return working_servers

def test_offline_capabilities() -> bool:
    """测试离线功能"""
    print("\n🎮 测试离线功能...")
    
    try:
        from game_engine import LatticeMessengersGame, Action
        from ai_model import AITrainer
        
        # 快速游戏测试
        game = LatticeMessengersGame()
        state = game.reset()
        
        red_actions = [Action.CHANT, Action.MOVE_RIGHT, Action.MOVE_DOWN]
        blue_actions = [Action.CHANT, Action.MOVE_LEFT, Action.MOVE_UP]
        
        state, rewards, done = game.step(red_actions, blue_actions)
        
        print("  ✅ 游戏引擎正常")
        
        # AI测试
        trainer = AITrainer()
        stats = trainer.get_training_stats()
        
        print("  ✅ AI系统正常")
        return True
        
    except Exception as e:
        print(f"  ❌ 离线功能测试失败: {e}")
        return False

def get_recommendations() -> List[Tuple[str, str, str]]:
    """获取推荐的启动方式"""
    recommendations = []
    
    # 检查各种启动方式
    if test_server_import('web_server_simple'):
        recommendations.append((
            "python start_simple.py",
            "简化启动",
            "最稳定，兼容性最好"
        ))
    
    if test_server_import('web_server'):
        recommendations.append((
            "python main.py",
            "标准启动",
            "功能完整，可能有兼容性问题"
        ))
    
    recommendations.append((
        "python offline_demo.py",
        "离线演示",
        "无需网络，纯命令行体验"
    ))
    
    recommendations.append((
        "python demo.py",
        "功能演示",
        "展示游戏各个组件"
    ))
    
    recommendations.append((
        "python test_basic.py",
        "基础测试",
        "验证核心功能"
    ))
    
    return recommendations

def main():
    """主测试函数"""
    print("🔮 晶格信使服务器兼容性测试")
    print("="*60)
    
    # 基础模块测试
    if not test_imports():
        print("\n❌ 基础模块测试失败")
        print("请运行: pip install -r requirements.txt")
        return False
    
    # WebSocket兼容性测试
    working_servers = test_websocket_compatibility()
    
    # 离线功能测试
    offline_ok = test_offline_capabilities()
    
    # 生成报告
    print("\n" + "="*60)
    print("📊 测试结果报告")
    print("="*60)
    
    print(f"✅ 可用的WebSocket服务器: {len(working_servers)}")
    for server in working_servers:
        print(f"   • {server}")
    
    print(f"{'✅' if offline_ok else '❌'} 离线功能: {'正常' if offline_ok else '异常'}")
    
    # 推荐启动方式
    print("\n🚀 推荐的启动方式:")
    recommendations = get_recommendations()
    
    for i, (command, name, description) in enumerate(recommendations, 1):
        print(f"{i}. {name}")
        print(f"   命令: {command}")
        print(f"   说明: {description}")
        print()
    
    # 总结
    if working_servers:
        print("🎉 您的环境支持Web界面版本!")
        print(f"推荐使用: {recommendations[0][0]}")
    elif offline_ok:
        print("⚠️ Web界面可能有问题，但离线功能正常")
        print("推荐使用: python offline_demo.py")
    else:
        print("❌ 环境配置有问题，请检查依赖安装")
        return False
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⏹️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        sys.exit(1)
