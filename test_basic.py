#!/usr/bin/env python3
"""
晶格信使基础功能测试
验证核心组件是否正常工作
"""

import sys
import traceback
from game_engine import LatticeMessengersGame, Action, Team, GameState, Messenger
from ai_model import MessengerNet, TeamAI, AITrainer
import config
import torch
import numpy as np

def test_config():
    """测试配置文件"""
    print("测试配置文件...")
    
    assert config.GRID_SIZE == 20, "网格大小配置错误"
    assert config.MAX_TURNS == 100, "最大回合数配置错误"
    assert config.MESSENGERS_PER_TEAM == 3, "信使数量配置错误"
    assert len(config.RED_INITIAL_POSITIONS) == 3, "红队初始位置数量错误"
    assert len(config.BLUE_INITIAL_POSITIONS) == 3, "蓝队初始位置数量错误"
    
    print("✅ 配置文件测试通过")

def test_messenger():
    """测试信使类"""
    print("测试信使类...")
    
    messenger = Messenger(5, 5, Team.RED, 0)
    assert messenger.x == 5 and messenger.y == 5, "信使位置初始化错误"
    assert messenger.team == Team.RED, "信使队伍设置错误"
    assert messenger.id == 0, "信使ID设置错误"
    
    # 测试移动
    success = messenger.move(Action.MOVE_RIGHT, 20)
    assert success, "有效移动失败"
    assert messenger.x == 6, "移动后位置错误"
    
    # 测试边界移动
    messenger.x = 19
    success = messenger.move(Action.MOVE_RIGHT, 20)
    assert not success, "边界移动应该失败"
    
    print("✅ 信使类测试通过")

def test_game_state():
    """测试游戏状态类"""
    print("测试游戏状态类...")
    
    state = GameState()
    assert state.grid_size == config.GRID_SIZE, "网格大小错误"
    assert state.max_turns == config.MAX_TURNS, "最大回合数错误"
    assert len(state.red_messengers) == config.MESSENGERS_PER_TEAM, "红队信使数量错误"
    assert len(state.blue_messengers) == config.MESSENGERS_PER_TEAM, "蓝队信使数量错误"
    
    # 测试状态向量
    state_vector = state.get_state_vector()
    expected_size = config.GRID_SIZE * config.GRID_SIZE + config.MESSENGERS_PER_TEAM * 2 * 2 + 1
    assert len(state_vector) == expected_size, f"状态向量大小错误: {len(state_vector)} != {expected_size}"
    
    # 测试动作执行
    messenger = state.red_messengers[0]
    old_energy = state.energy_grid[messenger.y, messenger.x]
    reward = state.execute_action(messenger, Action.CHANT)
    new_energy = state.energy_grid[messenger.y, messenger.x]
    assert new_energy == old_energy + config.ENERGY_CHANGE_PER_CHANT, "咏唱能量变化错误"
    assert reward == -config.CHANT_COST, "咏唱奖励错误"
    
    print("✅ 游戏状态类测试通过")

def test_game_engine():
    """测试游戏引擎"""
    print("测试游戏引擎...")
    
    game = LatticeMessengersGame()
    state = game.reset()
    
    assert not state.is_game_over(), "初始状态不应该结束"
    assert state.turn == 0, "初始回合数应该为0"
    
    # 测试游戏步骤
    red_actions = [Action.CHANT, Action.MOVE_RIGHT, Action.MOVE_DOWN]
    blue_actions = [Action.CHANT, Action.MOVE_LEFT, Action.MOVE_UP]
    
    new_state, rewards, done = game.step(red_actions, blue_actions)
    assert len(rewards) == 6, "奖励数量错误"
    assert new_state.turn == 1, "回合数未正确增加"
    
    print("✅ 游戏引擎测试通过")

def test_neural_network():
    """测试神经网络"""
    print("测试神经网络...")
    
    # 测试网络创建
    net = MessengerNet()
    assert net is not None, "网络创建失败"
    
    # 测试前向传播
    state_size = config.GRID_SIZE * config.GRID_SIZE + config.MESSENGERS_PER_TEAM * 2 * 2 + 1
    dummy_input = torch.randn(1, state_size)
    
    action_probs, value = net(dummy_input)
    assert action_probs.shape == (1, 5), f"动作概率形状错误: {action_probs.shape}"
    assert value.shape == (1, 1), f"价值形状错误: {value.shape}"
    assert torch.allclose(action_probs.sum(dim=1), torch.ones(1)), "动作概率和不为1"
    
    print("✅ 神经网络测试通过")

def test_team_ai():
    """测试团队AI"""
    print("测试团队AI...")
    
    ai = TeamAI(Team.RED)
    assert len(ai.networks) == config.MESSENGERS_PER_TEAM, "AI网络数量错误"
    assert len(ai.optimizers) == config.MESSENGERS_PER_TEAM, "优化器数量错误"
    
    # 测试动作获取
    state = GameState()
    actions = ai.get_actions(state, training=False)
    assert len(actions) == config.MESSENGERS_PER_TEAM, "动作数量错误"
    assert all(isinstance(action, Action) for action in actions), "动作类型错误"
    
    print("✅ 团队AI测试通过")

def test_ai_trainer():
    """测试AI训练器"""
    print("测试AI训练器...")
    
    trainer = AITrainer()
    assert trainer.red_ai is not None, "红队AI未创建"
    assert trainer.blue_ai is not None, "蓝队AI未创建"
    
    # 测试训练统计
    stats = trainer.get_training_stats()
    assert 'episodes' in stats, "统计信息缺少回合数"
    assert 'red_wins' in stats, "统计信息缺少红队胜利数"
    assert 'blue_wins' in stats, "统计信息缺少蓝队胜利数"
    
    print("✅ AI训练器测试通过")

def test_resonance_calculation():
    """测试共鸣链计算"""
    print("测试共鸣链计算...")
    
    state = GameState()
    
    # 创建一个简单的共鸣链
    state.energy_grid[0, 0:3] = 8.0  # 红色共鸣链
    state.energy_grid[1, 0:2] = -8.0  # 蓝色共鸣链
    
    red_score, blue_score = state.calculate_resonance_chains()
    assert red_score > 0, "红队共鸣链得分应该大于0"
    assert blue_score > 0, "蓝队共鸣链得分应该大于0"
    
    print("✅ 共鸣链计算测试通过")

def run_all_tests():
    """运行所有测试"""
    tests = [
        test_config,
        test_messenger,
        test_game_state,
        test_game_engine,
        test_neural_network,
        test_team_ai,
        test_ai_trainer,
        test_resonance_calculation
    ]
    
    passed = 0
    failed = 0
    
    print("🔮 晶格信使基础功能测试")
    print("="*50)
    
    for test_func in tests:
        try:
            test_func()
            passed += 1
        except Exception as e:
            print(f"❌ {test_func.__name__} 失败: {e}")
            traceback.print_exc()
            failed += 1
    
    print("\n" + "="*50)
    print(f"测试结果: {passed} 通过, {failed} 失败")
    
    if failed == 0:
        print("🎉 所有测试通过！游戏核心功能正常")
        return True
    else:
        print("⚠️ 部分测试失败，请检查代码")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
