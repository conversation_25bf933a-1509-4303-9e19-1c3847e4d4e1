#!/usr/bin/env python3
"""
晶格信使简化启动脚本
直接使用简化版WebSocket服务器，避免兼容性问题
"""

import sys
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def main():
    """主函数"""
    print("🔮 晶格信使 - 简化启动版本")
    print("="*50)
    
    try:
        # 检查依赖
        import torch
        import numpy
        import websockets
        logger.info("✅ 所有依赖检查通过")
    except ImportError as e:
        logger.error(f"❌ 缺少依赖: {e}")
        print("请运行: pip install torch numpy websockets")
        sys.exit(1)
    
    try:
        # 导入并启动简化服务器
        from web_server_simple import main as start_server
        logger.info("🚀 启动简化版WebSocket服务器...")
        print("游戏界面: http://localhost:8000")
        print("WebSocket: ws://localhost:8765")
        print("按 Ctrl+C 停止服务器")
        start_server()
    except KeyboardInterrupt:
        logger.info("服务器被用户中断")
    except Exception as e:
        logger.error(f"启动失败: {e}")
        print("\n💡 如果仍有问题，请尝试:")
        print("python offline_demo.py  # 离线演示版本")
        print("python test_basic.py    # 基础功能测试")
        sys.exit(1)

if __name__ == "__main__":
    main()
