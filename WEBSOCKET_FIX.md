# 🔧 WebSocket 连接问题修复指南

## 问题描述
如果您遇到以下错误：
```
TypeError: start_websocket_server.<locals>.<lambda>() missing 1 required positional argument: 'path'
```

这是由于不同版本的websockets库的API差异导致的。

## 快速修复方案

### 方案1: 使用简化服务器
项目已包含一个简化版本的WebSocket服务器，会自动切换：

```bash
python main.py
```

如果主服务器有问题，会自动使用 `web_server_simple.py`

### 方案2: 测试WebSocket连接
运行测试脚本检查连接状态：

```bash
python test_websocket.py
```

### 方案3: 手动使用简化服务器
直接运行简化版本：

```bash
python web_server_simple.py
```

### 方案4: 更新websockets库
尝试更新到最新版本：

```bash
pip install --upgrade websockets
```

或安装特定版本：

```bash
pip install websockets==10.4
```

## 版本兼容性

| websockets版本 | 兼容性 | 推荐 |
|---------------|--------|------|
| 11.x | ✅ 完全兼容 | ⭐ |
| 10.x | ✅ 完全兼容 | ⭐ |
| 9.x | ⚠️ 可能需要调整 | |
| 8.x | ❌ 不推荐 | |

## 故障排除步骤

### 1. 检查依赖
```bash
pip list | grep websockets
```

### 2. 重新安装依赖
```bash
pip uninstall websockets
pip install websockets>=10.0
```

### 3. 测试连接
```bash
python test_websocket.py
```

### 4. 检查端口占用
```bash
# Windows
netstat -ano | findstr :8765

# Linux/Mac
lsof -i :8765
```

### 5. 使用备用端口
编辑 `config.py`：
```python
WEBSOCKET_PORT = 8766  # 改为其他端口
```

## 常见错误及解决方案

### 错误1: Connection refused
**原因**: 服务器未启动或端口被占用
**解决**: 
- 确保运行了 `python main.py`
- 检查端口占用情况
- 尝试重启服务器

### 错误2: Module not found
**原因**: 缺少依赖包
**解决**: 
```bash
pip install -r requirements.txt
```

### 错误3: Permission denied
**原因**: 端口权限问题
**解决**: 
- 使用管理员权限运行
- 或修改为其他端口 (>1024)

### 错误4: Timeout
**原因**: 网络或防火墙问题
**解决**: 
- 检查防火墙设置
- 确保localhost访问正常
- 尝试使用127.0.0.1

## 备用运行方式

如果WebSocket仍有问题，可以使用以下备用方案：

### 1. 离线演示版
```bash
python offline_demo.py
```
- 不需要WebSocket
- 包含完整游戏功能
- 支持AI训练演示

### 2. 基础功能测试
```bash
python test_basic.py
```
- 验证核心功能
- 不依赖网络连接

### 3. 命令行演示
```bash
python demo.py
```
- 展示游戏机制
- 纯命令行界面

## 技术说明

WebSocket错误通常由以下原因引起：

1. **API变化**: 不同版本的websockets库API有差异
2. **异步处理**: Python异步编程的版本兼容性
3. **事件循环**: asyncio事件循环的处理方式

项目已提供多种兼容性解决方案，确保在各种环境下都能正常运行。

## 获取帮助

如果问题仍然存在：

1. 运行 `python test_websocket.py` 获取详细诊断
2. 检查 `lattice_messengers.log` 日志文件
3. 尝试使用 `python offline_demo.py` 体验核心功能

---

**记住**: 即使WebSocket有问题，游戏的核心AI功能仍然可以通过离线版本体验！ 🎮
