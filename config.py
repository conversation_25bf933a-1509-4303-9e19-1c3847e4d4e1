# 晶格信使游戏配置文件

# 游戏基础设置
GRID_SIZE = 20              # 网格大小 (20x20)
MAX_TURNS = 100             # 最大回合数
MAX_ENERGY = 10.0           # 最大能量值
MIN_ENERGY = -10.0          # 最小能量值

# 信使设置
MESSENGERS_PER_TEAM = 3     # 每队信使数量

# 红队初始位置
RED_INITIAL_POSITIONS = [
    (2, 2),
    (2, 17),
    (17, 2)
]

# 蓝队初始位置
BLUE_INITIAL_POSITIONS = [
    (17, 17),
    (17, 2),
    (2, 17)
]

# 动作成本
MOVE_COST = 0.1             # 移动消耗
CHANT_COST = 0.2            # 咏唱消耗
INVALID_MOVE_PENALTY = 0.5  # 无效移动惩罚

# 能量变化
ENERGY_CHANGE_PER_CHANT = 1.0  # 每次咏唱的能量变化

# 共鸣阈值
RED_RESONANCE_THRESHOLD = 5.0   # 红队共鸣阈值 (>5)
BLUE_RESONANCE_THRESHOLD = -5.0 # 蓝队共鸣阈值 (<-5)

# AI训练参数
LEARNING_RATE = 0.001       # 学习率
HIDDEN_SIZE = 256           # 隐藏层大小
MEMORY_SIZE = 10000         # 经验回放缓冲区大小
BATCH_SIZE = 32             # 批次大小
EPSILON_START = 1.0         # 初始探索率
EPSILON_MIN = 0.01          # 最小探索率
EPSILON_DECAY = 0.995       # 探索率衰减
GAMMA = 0.99                # 折扣因子

# 网络服务器设置
HTTP_PORT = 8000            # HTTP服务器端口
WEBSOCKET_PORT = 8765       # WebSocket服务器端口

# 可视化设置
ANIMATION_SPEED = 0.5       # 动画播放速度 (秒)
UPDATE_FREQUENCY = 10       # 训练统计更新频率 (每N个回合)
RESULT_FREQUENCY = 100      # 训练结果广播频率 (每N个回合)

# 调试设置
DEBUG_MODE = False          # 调试模式
LOG_LEVEL = "INFO"          # 日志级别
