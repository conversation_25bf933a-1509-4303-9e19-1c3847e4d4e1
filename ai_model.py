import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
import numpy as np
import random
from collections import deque
from typing import List, Tuple
from game_engine import Action, Team, LatticeMessengersGame, GameState
import config

class MessengerNet(nn.Module):
    """信使AI神经网络"""
    
    def __init__(self, input_size: int = 413, hidden_size: int = None, output_size: int = 5):
        if hidden_size is None:
            hidden_size = config.HIDDEN_SIZE
        super(MessengerNet, self).__init__()
        
        # 输入层：游戏状态向量 (400 + 12 + 1 = 413)
        self.fc1 = nn.Linear(input_size, hidden_size)
        self.fc2 = nn.Linear(hidden_size, hidden_size)
        self.fc3 = nn.Linear(hidden_size, hidden_size // 2)
        
        # 输出层：5个动作的概率
        self.action_head = nn.Linear(hidden_size // 2, output_size)
        
        # 价值函数头
        self.value_head = nn.Linear(hidden_size // 2, 1)
        
        self.dropout = nn.Dropout(0.1)
        
    def forward(self, x):
        x = F.relu(self.fc1(x))
        x = self.dropout(x)
        x = F.relu(self.fc2(x))
        x = self.dropout(x)
        x = F.relu(self.fc3(x))
        
        # 动作概率
        action_probs = F.softmax(self.action_head(x), dim=-1)
        
        # 状态价值
        value = self.value_head(x)
        
        return action_probs, value

class TeamAI:
    """团队AI控制器"""
    
    def __init__(self, team: Team, learning_rate: float = None):
        if learning_rate is None:
            learning_rate = config.LEARNING_RATE

        self.team = team
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

        # 为每个信使创建独立的网络
        self.networks = [MessengerNet().to(self.device) for _ in range(config.MESSENGERS_PER_TEAM)]
        self.optimizers = [optim.Adam(net.parameters(), lr=learning_rate) for net in self.networks]

        # 经验回放缓冲区
        self.memory = deque(maxlen=config.MEMORY_SIZE)
        self.batch_size = config.BATCH_SIZE

        # 探索参数
        self.epsilon = config.EPSILON_START
        self.epsilon_min = config.EPSILON_MIN
        self.epsilon_decay = config.EPSILON_DECAY
        
    def get_actions(self, state: GameState, training: bool = True) -> List[Action]:
        """获取所有信使的动作"""
        state_vector = torch.FloatTensor(state.get_state_vector()).unsqueeze(0).to(self.device)
        actions = []
        
        for i, network in enumerate(self.networks):
            if training and random.random() < self.epsilon:
                # 随机探索
                action = random.choice(list(Action))
            else:
                # 使用网络预测
                with torch.no_grad():
                    action_probs, _ = network(state_vector)
                    action_idx = torch.argmax(action_probs, dim=1).item()
                    action = Action(action_idx)
            
            actions.append(action)
        
        return actions
    
    def store_experience(self, state: np.ndarray, actions: List[Action], 
                        rewards: List[float], next_state: np.ndarray, done: bool):
        """存储经验"""
        self.memory.append((state, actions, rewards, next_state, done))
    
    def train(self):
        """训练网络"""
        if len(self.memory) < self.batch_size:
            return
        
        # 采样批次
        batch = random.sample(self.memory, self.batch_size)
        
        for messenger_idx in range(3):
            self._train_messenger(messenger_idx, batch)
        
        # 衰减探索率
        if self.epsilon > self.epsilon_min:
            self.epsilon *= self.epsilon_decay
    
    def _train_messenger(self, messenger_idx: int, batch: List):
        """训练特定信使的网络"""
        network = self.networks[messenger_idx]
        optimizer = self.optimizers[messenger_idx]
        
        states = torch.FloatTensor([exp[0] for exp in batch]).to(self.device)
        actions = torch.LongTensor([exp[1][messenger_idx].value for exp in batch]).to(self.device)
        rewards = torch.FloatTensor([exp[2][messenger_idx] for exp in batch]).to(self.device)
        next_states = torch.FloatTensor([exp[3] for exp in batch]).to(self.device)
        dones = torch.BoolTensor([exp[4] for exp in batch]).to(self.device)
        
        # 当前Q值
        action_probs, values = network(states)
        action_log_probs = torch.log(action_probs.gather(1, actions.unsqueeze(1)))
        
        # 目标Q值
        with torch.no_grad():
            _, next_values = network(next_states)
            targets = rewards.unsqueeze(1) + config.GAMMA * next_values * (~dones).unsqueeze(1)
        
        # 计算损失
        value_loss = F.mse_loss(values, targets)
        policy_loss = -(action_log_probs * (targets - values).detach()).mean()
        
        total_loss = value_loss + policy_loss
        
        # 反向传播
        optimizer.zero_grad()
        total_loss.backward()
        optimizer.step()

class AITrainer:
    """AI训练器"""
    
    def __init__(self):
        self.game = LatticeMessengersGame()
        self.red_ai = TeamAI(Team.RED)
        self.blue_ai = TeamAI(Team.BLUE)
        
        self.training_episodes = 0
        self.win_history = deque(maxlen=100)
        
    def train_episode(self) -> Tuple[int, int, int]:
        """训练一个回合，返回红队得分、蓝队得分、回合数"""
        state = self.game.reset()
        total_rewards_red = [0.0, 0.0, 0.0]
        total_rewards_blue = [0.0, 0.0, 0.0]
        
        while not state.is_game_over():
            # 获取当前状态
            current_state = state.get_state_vector()
            
            # 获取动作
            red_actions = self.red_ai.get_actions(state, training=True)
            blue_actions = self.blue_ai.get_actions(state, training=True)
            
            # 执行动作
            next_state, rewards, done = self.game.step(red_actions, blue_actions)
            next_state_vector = next_state.get_state_vector()
            
            # 存储经验
            red_rewards = rewards[:3]
            blue_rewards = rewards[3:]
            
            self.red_ai.store_experience(current_state, red_actions, red_rewards, 
                                       next_state_vector, done)
            self.blue_ai.store_experience(current_state, blue_actions, blue_rewards, 
                                        next_state_vector, done)
            
            # 累积奖励
            for i in range(3):
                total_rewards_red[i] += red_rewards[i]
                total_rewards_blue[i] += blue_rewards[i]
            
            state = next_state
        
        # 训练网络
        self.red_ai.train()
        self.blue_ai.train()
        
        # 获取最终得分
        red_score, blue_score = state.get_final_scores()
        
        # 记录胜负
        if red_score > blue_score:
            self.win_history.append(1)  # 红队胜
        elif blue_score > red_score:
            self.win_history.append(-1)  # 蓝队胜
        else:
            self.win_history.append(0)  # 平局
        
        self.training_episodes += 1
        
        return red_score, blue_score, state.turn
    
    def get_training_stats(self) -> dict:
        """获取训练统计信息"""
        if not self.win_history:
            return {
                'episodes': self.training_episodes,
                'red_wins': 0,
                'blue_wins': 0,
                'draws': 0,
                'red_epsilon': self.red_ai.epsilon,
                'blue_epsilon': self.blue_ai.epsilon
            }
        
        red_wins = sum(1 for x in self.win_history if x == 1)
        blue_wins = sum(1 for x in self.win_history if x == -1)
        draws = sum(1 for x in self.win_history if x == 0)
        
        return {
            'episodes': self.training_episodes,
            'red_wins': red_wins,
            'blue_wins': blue_wins,
            'draws': draws,
            'red_epsilon': self.red_ai.epsilon,
            'blue_epsilon': self.blue_ai.epsilon
        }
    
    def play_game(self) -> Tuple[GameState, List[dict]]:
        """进行一场展示游戏，返回最终状态和历史记录"""
        state = self.game.reset()
        history = []
        
        while not state.is_game_over():
            # 记录当前状态
            history.append(state.get_game_info())
            
            # 获取动作（不探索）
            red_actions = self.red_ai.get_actions(state, training=False)
            blue_actions = self.blue_ai.get_actions(state, training=False)
            
            # 执行动作
            state, _, _ = self.game.step(red_actions, blue_actions)
        
        # 记录最终状态
        history.append(state.get_game_info())
        
        return state, history
