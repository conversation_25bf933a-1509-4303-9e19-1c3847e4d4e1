# 🚀 晶格信使快速开始指南

## ✅ WebSocket问题已修复！

最新版本已经解决了所有WebSocket兼容性问题，现在可以正常使用所有功能。

## 一键启动 (推荐)

### Windows
```bash
start.bat
```

### Linux/Mac
```bash
./start.sh
```

## 手动启动

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 启动游戏
```bash
python main.py
```

### 3. 访问界面
打开浏览器访问: http://localhost:8000

## 验证修复状态

### 检查WebSocket是否正常
```bash
# 验证修复是否成功
python test_websocket_fix.py

# 测试所有启动方式
python test_all_servers.py

# 测试连接
python test_websocket.py
```

## 如果仍有问题

### 依赖安装失败
```bash
# 使用最小依赖
pip install -r requirements_minimal.txt

# 手动安装
pip install torch numpy websockets
```

### 无法访问Web界面
```bash
# 使用离线演示版
python offline_demo.py

# 运行基础测试
python test_basic.py

# 功能演示
python demo.py
```

## 游戏操作

### Web界面控制
- **开始训练**: 让AI开始学习对战策略
- **停止训练**: 暂停AI训练过程  
- **观看对战**: 观看训练好的AI实时对战

### 键盘快捷键
- `S` - 开始训练
- `X` - 停止训练
- `P` - 观看对战

## 备用运行方式

| 方式 | 命令 | 说明 |
|------|------|------|
| 自动启动 | `python main.py` | 自动选择最佳服务器 |
| 简化启动 | `python start_simple.py` | 直接使用简化服务器 |
| 兼容测试 | `python test_all_servers.py` | 测试所有启动方式 |
| 离线版 | `python offline_demo.py` | 不需要网络，纯命令行 |
| 测试版 | `python test_basic.py` | 验证核心功能 |
| 演示版 | `python demo.py` | 展示游戏机制 |

## 故障排除

### 常见问题
1. **端口被占用**: 修改 `config.py` 中的端口设置
2. **依赖缺失**: 运行 `pip install -r requirements.txt`
3. **权限问题**: 使用管理员权限运行
4. **防火墙阻止**: 添加端口8000和8765到例外

### 获取帮助
- 查看 `INSTALL_GUIDE.md` 详细安装指南
- 查看 `WEBSOCKET_FIX.md` WebSocket问题解决
- 运行 `python test_websocket.py` 诊断连接问题

## 项目文件说明

### 核心文件
- `main.py` - 主启动入口
- `game_engine.py` - 游戏核心逻辑
- `ai_model.py` - AI神经网络
- `web_server.py` - Web服务器
- `index.html` - 前端界面

### 配置文件
- `config.py` - 游戏参数配置
- `requirements.txt` - Python依赖

### 工具脚本
- `test_*.py` - 各种测试脚本
- `demo.py` - 功能演示
- `offline_demo.py` - 离线版本

### 文档
- `README.md` - 详细说明
- `INSTALL_GUIDE.md` - 安装指南
- `WEBSOCKET_FIX.md` - 问题修复
- `PROJECT_SUMMARY.md` - 项目总结

---

**开始您的AI进化之旅！** 🔮✨
