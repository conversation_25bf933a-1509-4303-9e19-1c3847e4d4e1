# 🔮 晶格信使安装指南

## 快速开始 (推荐)

### Windows用户
1. 双击运行 `start.bat`
2. 脚本会自动检查环境并安装依赖
3. 启动成功后访问 http://localhost:8000

### Linux/Mac用户
1. 运行 `./start.sh` (或 `bash start.sh`)
2. 脚本会自动检查环境并安装依赖
3. 启动成功后访问 http://localhost:8000

## 手动安装

### 1. 检查Python环境
确保已安装Python 3.8或更高版本：
```bash
python --version
# 或
python3 --version
```

### 2. 安装依赖包

**方法A: 标准安装**
```bash
pip install -r requirements.txt
```

**方法B: 最小依赖 (网络问题时)**
```bash
pip install -r requirements_minimal.txt
```

**方法C: 手动安装**
```bash
pip install torch numpy websockets
```

### 3. 启动游戏
```bash
python main.py
```

### 4. 访问界面
打开浏览器访问: http://localhost:8000

## 故障排除

### 问题1: pip安装失败
**症状**: `ERROR: Could not find a version that satisfies the requirement`

**解决方案**:
```bash
# 升级pip
python -m pip install --upgrade pip

# 使用国内镜像
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple torch numpy websockets

# 或使用conda
conda install pytorch numpy
pip install websockets
```

### 问题2: PyTorch安装失败
**症状**: PyTorch下载缓慢或失败

**解决方案**:
1. 访问 https://pytorch.org 获取适合您系统的安装命令
2. 对于CPU版本: `pip install torch --index-url https://download.pytorch.org/whl/cpu`
3. 使用conda: `conda install pytorch cpuonly -c pytorch`

### 问题3: 端口被占用
**症状**: `Address already in use` 或 `端口被占用`

**解决方案**:
```bash
# 查找占用端口的进程
netstat -ano | findstr :8000
netstat -ano | findstr :8765

# 结束进程 (Windows)
taskkill /PID <进程ID> /F

# 结束进程 (Linux/Mac)
kill -9 <进程ID>
```

### 问题4: 防火墙阻止
**症状**: 无法访问Web界面

**解决方案**:
1. 临时关闭防火墙测试
2. 或添加端口8000和8765到防火墙例外
3. 检查杀毒软件是否阻止

## 备用运行方式

如果完整版本无法运行，可以尝试以下备用方案：

### 1. 离线演示版
```bash
python offline_demo.py
```
- 不需要websockets依赖
- 包含核心游戏功能
- 支持AI训练演示

### 2. 基础功能测试
```bash
python test_basic.py
```
- 验证所有组件是否正常
- 快速定位问题

### 3. 功能演示
```bash
python demo.py
```
- 展示游戏各个功能
- 了解游戏机制

## 系统要求

### 最低要求
- Python 3.8+
- 2GB RAM
- 100MB 磁盘空间

### 推荐配置
- Python 3.9+
- 4GB RAM
- 支持GPU的PyTorch (可选)

### 支持的操作系统
- Windows 10/11
- macOS 10.15+
- Ubuntu 18.04+
- 其他Linux发行版

## 依赖包说明

| 包名 | 版本 | 用途 |
|------|------|------|
| torch | >=1.9.0 | 深度学习框架 |
| numpy | >=1.20.0 | 数值计算 |
| websockets | >=10.0 | 实时通信 |

## 网络配置

### 默认端口
- HTTP服务器: 8000
- WebSocket服务器: 8765

### 修改端口
编辑 `config.py` 文件：
```python
HTTP_PORT = 8080        # 修改HTTP端口
WEBSOCKET_PORT = 8766   # 修改WebSocket端口
```

## 性能优化

### CPU优化
- 关闭不必要的后台程序
- 使用较小的批次大小训练

### 内存优化
- 减少经验回放缓冲区大小
- 降低神经网络隐藏层大小

### GPU加速 (可选)
```bash
# 安装GPU版本PyTorch
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
```

## 开发环境设置

### 代码编辑器推荐
- Visual Studio Code
- PyCharm
- Sublime Text

### 调试模式
```bash
python main.py --debug
```

### 修改配置
编辑 `config.py` 文件可以调整游戏参数：
- 网格大小
- 训练参数
- 可视化设置

## 获取帮助

如果遇到问题：
1. 查看本安装指南
2. 运行 `python test_basic.py` 检测问题
3. 尝试离线演示版本 `python offline_demo.py`
4. 检查Python和依赖包版本

## 卸载

删除项目文件夹即可完成卸载。如需清理Python包：
```bash
pip uninstall torch numpy websockets
```

---

**祝您游戏愉快！** 🎮✨
