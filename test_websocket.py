#!/usr/bin/env python3
"""
WebSocket连接测试脚本
用于验证WebSocket服务器是否正常工作
"""

import asyncio
import json
import sys

try:
    import websockets
    WEBSOCKETS_AVAILABLE = True
except ImportError:
    WEBSOCKETS_AVAILABLE = False
    print("❌ websockets库未安装")
    print("请运行: pip install websockets")

async def test_websocket_connection():
    """测试WebSocket连接"""
    if not WEBSOCKETS_AVAILABLE:
        return False
    
    try:
        print("🔗 尝试连接WebSocket服务器...")
        
        # 连接到WebSocket服务器
        uri = "ws://localhost:8765"
        async with websockets.connect(uri) as websocket:
            print("✅ WebSocket连接成功!")
            
            # 发送测试消息
            test_message = {
                'type': 'get_stats'
            }
            
            await websocket.send(json.dumps(test_message))
            print("📤 已发送测试消息")
            
            # 等待响应
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                data = json.loads(response)
                print(f"📥 收到响应: {data['type']}")
                
                if data['type'] == 'training_stats':
                    stats = data['data']
                    print(f"   训练回合: {stats['episodes']}")
                    print(f"   红队胜利: {stats['red_wins']}")
                    print(f"   蓝队胜利: {stats['blue_wins']}")
                    print("✅ WebSocket通信测试成功!")
                    return True
                    
            except asyncio.TimeoutError:
                print("⏰ 等待响应超时")
                return False
                
    except ConnectionRefusedError:
        print("❌ 连接被拒绝 - 服务器可能未启动")
        print("请先运行: python main.py")
        return False
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        return False

def test_imports():
    """测试必要的模块导入"""
    print("🔍 测试模块导入...")
    
    try:
        import torch
        print("✅ PyTorch 导入成功")
    except ImportError:
        print("❌ PyTorch 导入失败")
        return False
    
    try:
        import numpy
        print("✅ NumPy 导入成功")
    except ImportError:
        print("❌ NumPy 导入失败")
        return False
    
    try:
        import websockets
        print("✅ WebSockets 导入成功")
    except ImportError:
        print("❌ WebSockets 导入失败")
        return False
    
    try:
        from game_engine import LatticeMessengersGame
        print("✅ 游戏引擎 导入成功")
    except ImportError as e:
        print(f"❌ 游戏引擎 导入失败: {e}")
        return False
    
    try:
        from ai_model import AITrainer
        print("✅ AI模型 导入成功")
    except ImportError as e:
        print(f"❌ AI模型 导入失败: {e}")
        return False
    
    return True

async def main():
    """主测试函数"""
    print("🔮 晶格信使 WebSocket 测试")
    print("="*50)
    
    # 测试模块导入
    if not test_imports():
        print("\n❌ 模块导入测试失败")
        return False
    
    print("\n" + "="*50)
    
    # 测试WebSocket连接
    if await test_websocket_connection():
        print("\n🎉 所有测试通过!")
        return True
    else:
        print("\n❌ WebSocket连接测试失败")
        print("\n💡 故障排除建议:")
        print("1. 确保服务器正在运行: python main.py")
        print("2. 检查端口8765是否被占用")
        print("3. 检查防火墙设置")
        print("4. 尝试重启服务器")
        return False

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n\n⏹️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        sys.exit(1)
