@echo off
chcp 65001 >nul
title 晶格信使 - The Lattice Messengers

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                                                              ║
echo ║                    🔮 晶格信使 🔮                              ║
echo ║                The Lattice Messengers                        ║
echo ║                                                              ║
echo ║                   AI进化模拟游戏                              ║
echo ║                                                              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 正在检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python环境
    echo 请先安装Python 3.8或更高版本
    pause
    exit /b 1
)

echo ✅ Python环境检查通过

echo.
echo 正在检查依赖包...
pip show torch >nul 2>&1
if errorlevel 1 (
    echo 📦 正在安装依赖包...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ⚠️ 标准依赖安装失败，尝试最小依赖...
        pip install -r requirements_minimal.txt
        if errorlevel 1 (
            echo ❌ 依赖包安装失败
            echo 💡 您可以尝试运行离线演示版本: python offline_demo.py
            pause
            exit /b 1
        )
    )
) else (
    echo ✅ 依赖包检查通过
)

echo.
echo 🚀 正在启动晶格信使游戏服务器...
echo.
echo 游戏界面: http://localhost:8000
echo 按 Ctrl+C 停止服务器
echo.

python main.py

echo.
echo 游戏服务器已关闭
pause
